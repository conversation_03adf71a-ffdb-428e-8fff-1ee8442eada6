{"name": "individuallist.xyz", "version": "0.0.0", "private": true, "workspaces": ["packages/*"], "scripts": {"clean": "npm run clean --workspaces --if-present && rm -rf node_modules package-lock.json", "build": "npm run build --workspaces --if-present", "build:lib": "npm run build -w lib-worker", "lint:fix": "npm run lint:fix --workspaces --if-present", "publish:api": "npm run publish -w mailgun -w consumer -w queue-note -w queue-profile -w wspool -w media", "publish": "npm run publish --workspaces --if-present", "site": "npm run dev -w site", "site:local": "npm run dev:local -w site"}, "engines": {"node": ">=16"}, "dependencies": {"lodash-es": "^4.17.21"}, "devDependencies": {"@types/lodash-es": "^4.17.12"}}