name = "individuallist-xyz"
pages_build_output_dir = "./dist"
compatibility_date = "2024-06-15"
upload_source_maps = true

[[kv_namespaces]]
binding = "OTP"
id = "0b517acfad884fc6b4b345f5d669eed3"
preview_id = "0b517acfad884fc6b4b345f5d669eed3"

[[services]]
binding = "WS"
service = "wspool"

[[services]]
binding = "MAIL"
service = "mailgun"

[[queues.producers]]
queue = "deliver-queue"
binding = "QUEUE"

[[queues.producers]]
queue = "note-queue"
binding = "QUEUE_NOTE"

[[queues.producers]]
queue = "queue-profile"
binding = "QUEUE_PROFILE"

[[d1_databases]]
binding = "DB"
database_name = "db"
database_id = "b69dc291-58a2-474d-9a92-f3c58cf03c38"
preview_database_id = "b69dc291-58a2-474d-9a92-f3c58cf03c38"

[[r2_buckets]]
bucket_name = "public"
preview_bucket_name = "public-dev"
binding = "BUCKET"

[[vectorize]]
binding = "VECTORIZE_INDEX"
index_name = "vector-index"

[ai]
binding = "AI"

[vars]
BACKBLAZE_KEY_ID = "0040c7bb10e89d20000000006"
BACKBLAZE_BUCKET_ID = "d05c67ebeba130fe88c90d12"

# The necessary secrets are:
#
# - BACKBLAZE_KEY
# - OPENAI_API_KEY
#
# - USER_KEY
#   Secret used to encrypt user private key in the database
#   openssl rand -base64 32
#
# - SESSION_SECRET
#   Secret used for session encryption
#   openssl rand -base64 32
