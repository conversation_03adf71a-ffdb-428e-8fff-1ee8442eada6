#!/bin/bash

# Script to generate multiple accounts with posts using Wrangler local DB
# Usage: ./scripts/generate-accounts.sh [number_of_accounts] [posts_per_account]

set -e

NUM_ACCOUNTS=${1:-5}
POSTS_PER_ACCOUNT=${2:-3}
DOMAIN="individuallist.xyz"
SQL_FILE="$(mktemp)"

# Array of random usernames
USERNAMES=(
  "testuser"
  "developer"
  "designer"
  "writer"
  "artist"
  "musician"
  "photographer"
  "coder"
  "blogger"
  "traveler"
  "foodie"
  "gamer"
  "athlete"
  "teacher"
  "student"
)

# Array of random post contents
POST_CONTENTS=(
  "<p>Just testing out this platform. Looks promising!</p>"
  "<p>Hello world! This is my first post here.</p>"
  "<p>Working on a new project. More details coming soon.</p>"
  "<p>Excited to join this community!</p>"
  "<p>Sharing some thoughts on technology and design.</p>"
  "<p>What a beautiful day today!</p>"
  "<p>Just finished reading an interesting book about AI.</p>"
  "<p>Learning new programming languages is always fun.</p>"
  "<p>Coffee break time. ☕</p>"
  "<p>Weekend project: building a new website.</p>"
  "<p>Just discovered a great new podcast.</p>"
  "<p>Working from home today.</p>"
  "<p>Thinking about the future of social media.</p>"
  "<p>Trying out some new recipes this week.</p>"
  "<p>Planning my next vacation.</p>"
)

# Function to generate a random string
generate_random_string() {
  local length=${1:-8}
  local result=""
  local chars="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

  for ((i=0; i<length; i++)); do
    local rand=$((RANDOM % ${#chars}))
    result+="${chars:$rand:1}"
  done

  echo "$result"
}

# Function to generate a UUID
generate_uuid() {
  if command -v uuidgen &> /dev/null; then
    uuidgen | tr '[:upper:]' '[:lower:]'
  else
    python -c 'import uuid; print(str(uuid.uuid4()))'
  fi
}

# Function to get current timestamp in ISO format
get_timestamp() {
  date -u +"%Y-%m-%dT%H:%M:%S.000Z"
}

# Start generating the SQL file
echo "BEGIN TRANSACTION;" > "$SQL_FILE"

echo "Generating $NUM_ACCOUNTS accounts with $POSTS_PER_ACCOUNT posts each using Wrangler local DB..."

for ((i=1; i<=NUM_ACCOUNTS; i++)); do
  username_index=$((RANDOM % ${#USERNAMES[@]}))
  username="${USERNAMES[$username_index]}$(generate_random_string 4)"
  email="${username}@$DOMAIN"
  actor_id="https://$DOMAIN/ap/users/$username"

  echo "Creating account for $username ($email)..."

  # Insert actor into the database
  cat >> "$SQL_FILE" << EOF
INSERT INTO actors (
  id,
  type,
  email,
  pubkey,
  properties
) VALUES (
  '$actor_id',
  'Person',
  '$email',
  '',
  '{"preferredUsername":"$username", "name":"$username", "inbox":"$actor_id/inbox", "outbox":"$actor_id/outbox", "following":"$actor_id/following", "followers":"$actor_id/followers"}'
);
EOF

  echo "Account created for $username with ID: $actor_id"

  # Create posts for this account
  for ((j=1; j<=POSTS_PER_ACCOUNT; j++)); do
    # Get a random post content
    content_index=$((RANDOM % ${#POST_CONTENTS[@]}))
    content="${POST_CONTENTS[$content_index]}"
    object_id=$(generate_uuid)
    mastodon_id=$(generate_uuid)
    published_date=$(get_timestamp)
    outbox_id=$(generate_uuid)

    echo "Creating post $j for $username..."

    # Insert object into the database
    cat >> "$SQL_FILE" << EOF
INSERT INTO objects (
  id,
  type,
  properties,
  local,
  mastodon_id
) VALUES (
  '$object_id',
  'Note',
  '{"content":"$content", "published":"$published_date"}',
  1,
  '$mastodon_id'
);

INSERT INTO outbox_objects (
  id,
  actor_id,
  object_id,
  target,
  published_date
) VALUES (
  '$outbox_id',
  '$actor_id',
  '$object_id',
  'https://www.w3.org/ns/activitystreams#Public',
  '$published_date'
);
EOF

    echo "Post $j created with ID: $object_id"
  done

  echo "Completed account $i of $NUM_ACCOUNTS"
  echo "-----------------------------------"
done

# Finish the transaction
echo "COMMIT;" >> "$SQL_FILE"

# Execute the SQL file
echo "Executing SQL file..."
npx wrangler d1 execute DB --local --file="$SQL_FILE"

# Clean up
# rm "$SQL_FILE"

echo "All done! Created $NUM_ACCOUNTS accounts with $POSTS_PER_ACCOUNT posts each."
