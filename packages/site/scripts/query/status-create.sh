#!/bin/bash

TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************.A803pB8s5zryWbWRiEW8fbH9QvkTxj0a4IghMxmLomU

curl 'http://localhost:8787/api/v1/statuses' \
  -X 'POST' \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer ${TOKEN}" \
  --data-raw '{"status":"<p>qwerty</p><div>one<div>one inner</div></div><div>second</div><img src=\"image_url\"/>","visibility":"public","sensitive":false,"spoilerText":"","language":"","media_ids":[]}' \
  --compressed
