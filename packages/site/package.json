{"name": "site", "private": true, "type": "module", "scripts": {"clean": "rm -rf node_modules package-lock.json .nuxt dist functions", "upgrade": "nuxt upgrade --force", "lint:fix": "eslint . --fix", "build": "nuxt build", "db:migrations": "", "dev:local": "wrangler d1 migrations apply DB --local && nuxt dev", "dev": "API=https://individuallist.xyz nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "serve": "wrangler pages dev dist --compatibility-date=2022-11-21", "publish": "wrangler pages deploy dist --project-name=individuallist-xyz --commit-dirty=true", "postpublish": "npm-run-all --parallel postpublish:*", "postpublish:vars": "echo $BACKBLAZE_KEY | wrangler pages secret put BACKBLAZE_KEY", "logs": "wrangler pages deployment tail --project-name=individuallist-xyz", "postinstall": "nuxt prepare"}, "dependencies": {"@fluent/bundle": "^0.18.0", "@formkit/auto-animate": "^0.8.2", "@tiptap/extension-link": "^2.9.1", "@tiptap/extension-mention": "^2.9.1", "@tiptap/extension-placeholder": "^2.9.1", "@tiptap/extension-underline": "^2.9.1", "@tiptap/starter-kit": "^2.9.1", "@tiptap/suggestion": "^2.9.1", "@tiptap/vue-3": "^2.9.1", "@vueuse/core": "^11.1.0", "axios": "^1.7.7", "color": "^4.2.3", "culori": "^4.0.1", "dayjs": "^1.11.13", "floating-vue": "^5.2.2", "fluent-vue": "^3.6.0", "linkifyjs": "^4.1.3", "lru-cache": "^11.0.2", "luxon": "^3.5.0", "page-lifecycle": "^0.1.2", "portal-vue": "^3.0.0", "tiny-decode": "^0.1.3", "tippy.js": "^6.3.7", "ultrahtml": "^1.5.3", "vue-component-type-helpers": "^2.1.10", "vue-tippy": "^6.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241112.0", "@nuxt/eslint": "^0.5.7", "@sentry/vue": "^8.38.0", "@types/color": "^3.0.6", "@vueuse/nuxt": "^11.2.0", "eslint": "^9.14.0", "eslint-plugin-regexp": "^2.6.0", "nitro-cloudflare-dev": "^0.2.1", "nuxt": "^3.14.159", "sass-embedded": "^1.80.7", "toucan-js": "^4.0.0", "wrangler": "^3.86.1"}}