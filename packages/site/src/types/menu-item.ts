import type { RouteLocation } from '#vue-router'

export type MenuItem<T = unknown> = {
  icon?: string
  label: string
  desc?: string
  checked?: boolean
  to?: string | RouteLocation
  command?: (_item: MenuItem<T>) => void | Promise<void>
  items?: MenuItem<T>[]
  separator?: boolean
  meta?: T
}

export type SelectOption<T> = {
  value: T
  icon?: string
  label?: string
  desc?: string
  checked?: boolean
}

type MenuType = ComponentPublicInstance & {
  focus: () => void
  blur: () => void
}

export type UiPopupMenuType = MenuType & {
  isOpen: () => void
  show: () => void
  hide: () => void
  focusTrigger: () => void
}

export type UiMenuListType = MenuType & {
  onKeyDown: (event: KeyboardEvent) => void
}

export type UiMenuItemType = MenuType & {
  isOpenSubmenu: () => boolean
  showSubmenu: () => void
  hideSubmenu: () => void
  focusSubmenu: () => void
}
