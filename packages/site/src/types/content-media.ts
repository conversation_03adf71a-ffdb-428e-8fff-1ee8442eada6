export type MediaTypeStr = 'image' | 'video' | 'audio' | 'file'

type Media = {
  src: string
  id: string
  publishId?: string
  error?: any
}

export type WaveformData = {
  wave?: number[]
  duration?: number
}

export type ContentMediaImage = Media & {
  type: 'image'
  aspect?: number
}

export type ContentMediaFile = Media & {
  type: 'file'
  fileType?: string
  posterUrl?: string
}

export type ContentMediaVideo = Media & {
  type: 'video'
  duration?: number
  posterUrl?: string
  aspect?: number
  posterTime?: number
}

export type ContentMediaAudio = Media & WaveformData & {
  type: 'audio'
}

export type ContentMedia = ContentMediaImage | ContentMediaVideo | ContentMediaAudio | ContentMediaFile

export type UploadContentMedia = ContentMedia & { file: File }

export type WaveformDataFn = (mediaId: string, data: WaveformData) => void
export type VideoPosterFn = (file: File) => void
