export type SearchType = 'accounts' | 'hashtags' | 'statuses'

export interface DefaultPaginationParams {
  /** Return results older than this ID. */
  readonly maxId?: string | null
  /** Return results newer than this ID. */
  readonly sinceId?: string | null
  /** Get a list of items with ID greater than this value excluding this ID */
  readonly minId?: string | null
  /** Maximum number of results to return per page. Defaults to 40. NOTE: Pagination is done with the Link header from the response. */
  readonly limit?: number | null
}

export interface SearchParams extends DefaultPaginationParams {
  /** Attempt WebFinger lookup. Defaults to false. */
  readonly q: string
  /** Enum(accounts, hashtags, statuses) */
  readonly type?: SearchType | null
  /** Attempt WebFinger look-up */
  readonly resolve?: boolean | null
  /** If provided, statuses returned will be authored only by this account */
  readonly accountId?: string | null
  /** Filter out unreviewed tags? Defaults to false. Use true when trying to find trending tags. */
  readonly excludeUnreviewed?: boolean | null
  /** Only include accounts that the user is following. Defaults to false. */
  readonly following?: boolean | null
}
