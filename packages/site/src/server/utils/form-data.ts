import type { H3Event, InferEventInput } from 'h3'
import { readMultipartFormData, createError } from 'h3'

export type ValidateResult<T> = T | true | false

export type ValidateFunction<T> = (data: unknown) => ValidateResult<T> | Promise<ValidateResult<T>>

export async function readValidatedFormData<
  T,
  Event extends H3Event = H3Event,
  _T = InferEventInput<'body', Event, T>,
>(event: Event, fn: ValidateFunction<_T>): Promise<_T> {
  const formData = await readMultipartFormData(event)
  if (!formData) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Bad Request',
      message: 'No form data found in the request',
    })
  }

  // Convert form data to an object
  const data: Record<string, unknown> = {}
  for (const field of formData) {
    // Handle file uploads
    if (field.filename) {
      data[field.name ?? field.filename] = new File(
        [field.data],
        field.filename,
        { type: field.type || 'application/octet-stream' },
      )
    }
    else if (field.name) {
      // Handle text fields
      if (typeof field.data === 'string' || field.data instanceof Uint8Array) {
        data[field.name] = field.data.toString()
      }
      // Handle other types of data
      else {
        data[field.name] = field.data
      }
    }
  }

  try {
    const res = await fn(data)
    if (res === false) {
      throw createValidationError()
    }
    if (res === true) {
      return data as _T
    }
    return res ?? (data as _T)
  }
  catch (error) {
    throw createValidationError(error)
  }
}

function createValidationError(validateError?: unknown) {
  throw createError({
    status: 400,
    statusMessage: 'Validation Error',
    message: (validateError as Error)?.message || 'Validation Error',
    data: validateError,
    cause: validateError,
  })
}
