export async function similar(db: D1Database, indexDb: VectorizeIndex, id: string, count = 5) {
  const [vectors] = await indexDb.getByIds([id])
  if (!vectors) {
    return []
  }
  const { matches } = await indexDb.query(vectors.values, { topK: count, returnMetadata: true })
  console.log('matches', matches)
  return matches.filter(({ vectorId }) => vectorId !== id).map<string>(({ vectorId }) => vectorId)
}
