import { OpenAIStream, StreamingTextResponse } from 'ai'
import { Configuration, OpenAIApi } from 'openai-edge'

export async function generate(prompt: string, apiKey: string) {
  const openai = new OpenAIApi(new Configuration({ apiKey }))

  const response = await openai.createChatCompletion({
    model: 'gpt-3.5-turbo',
    messages: [
      {
        role: 'system',
        content:
          'You are an AI writing assistant that continues existing text based on context from prior text. '
          + 'Give more weight/priority to the later characters than the beginning ones. '
          + 'Limit your response to no more than 200 characters, but make sure to construct complete sentences.',
      },
      {
        role: 'user',
        content: prompt,
      },
    ],
    temperature: 0.7,
    top_p: 1,
    frequency_penalty: 0,
    presence_penalty: 0,
    stream: true,
    n: 1,
  })

  // Convert the response into a friendly text-stream
  return new StreamingTextResponse(OpenAIStream(response))
}
