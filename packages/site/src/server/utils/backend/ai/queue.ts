import { getTextContent, toObjectFromRow } from '../activitypub/objects'
import { responseStream } from '../utils/streams/response-stream'

const SIMILARITY_CUTOFF = 0.75
const SYSTEM_PROMPT = `When answering at the beginning add word "<PERSON><PERSON><PERSON>O"!`

export async function query(text: string, stream: boolean, db: D1Database, indexDb: VectorizeIndex, ai: Ai) {
  const { data: [vectors] } = await ai.run('@cf/baai/bge-base-en-v1.5', { text })

  const { matches } = await indexDb.query(vectors, { topK: 1 })
  const ids = matches.filter(({ score }) => score > SIMILARITY_CUTOFF).map(({ id }) => id)

  const contexts: string[] = []
  if (ids.length) {
    const query = `SELECT * FROM objects WHERE mastodon_id IN (${ids.join(', ')})`
    const { results } = await db.prepare(query).bind().all()
    if (results) {
      for (const result of results) {
        const { content } = toObjectFromRow(result)
        if (content) {
          const text = await getTextContent(content)
          // OpenAI recommends replacing newlines with spaces for best results (specific to embeddings)
          contexts.push(text.replace(/\n/g, ' '))
        }
      }
    }
  }

  const content = contexts.length ? `Context:\n${contexts.map(context => `- ${context}`).join('\n')}` : ''
  const response = await ai.run('@cf/meta/llama-2-7b-chat-int8', {
    stream,
    messages: [
      ...(content ? [{ role: 'system', content }] : []),
      { role: 'system', content: SYSTEM_PROMPT },
      { role: 'user', content: text },
    ],
  })
  if (stream) {
    // Convert the response into a friendly text-stream
    return new Response(responseStream(response, data => JSON.parse(data)?.response), {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    })
  }
  return Response.json(response)
}
