interface ServerAboutData {
  'extended description': string
  'privacy policy': string
}
interface ServerBrandingData {
  'server name': string
  'server description': string
}

type ServerSettingsData = ServerBrandingData & ServerAboutData

export async function getSettings(db: D1Database): Promise<ServerSettingsData> {
  const query = `SELECT * from server_settings`
  const result = await db.prepare(query).all<{ setting_name: string, setting_value: string }>()

  const data = (result.results ?? []).reduce(
    (settings, { setting_name, setting_value }) => ({
      ...settings,
      [setting_name]: setting_value,
    }),
    {} as object,
  )

  if (!result.success) {
    throw new Error('SQL Error: ' + result.error)
  }

  return data as ServerSettingsData
}

export async function updateSettings(db: D1Database, data: Partial<ServerSettingsData>) {
  const result = await upsertServerSettings(db, data)
  if (result && !result.success) {
    throw new Error('SQL Error: ' + result.error)
  }

  return new Response('', { status: 200 })
}

export async function upsertServerSettings(db: D1Database, settings: Partial<ServerSettingsData>) {
  const settingsEntries = Object.entries(settings)

  if (!settingsEntries.length) {
    return null
  }

  const query = `
  INSERT INTO server_settings (setting_name, setting_value)
  VALUES ${settingsEntries.map(() => `(?, ?)`).join(', ')}
  ON CONFLICT(setting_name) DO UPDATE SET setting_value=excluded.setting_value`

  return await db
    .prepare(query)
    .bind(...settingsEntries.flat())
    .run()
}
