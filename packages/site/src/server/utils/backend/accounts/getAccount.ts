// https://docs.joinmastodon.org/methods/accounts/#get

import { adjustLocalHostDomain } from '../utils/adjustLocalHostDomain'
import { actorURL, getActorById } from '~/server/utils/backend/activitypub/actors'
import { isLocalHandle, parseHandle } from '~/server/utils/backend/utils/parse'
import type { Handle } from '~/server/utils/backend/utils/parse'
import { queryAcct } from '~/server/utils/backend/webfinger/index'
import { loadExternalMastodonAccount, loadLocalMastodonAccount } from '~/server/utils/backend/mastodon/account'

export function getAccount(domain: string, accountId: string, db: D1Database, loadStats: boolean = true) {
  const handle = parseHandle(accountId)

  if (isLocalHandle(handle, domain)) {
    // Retrieve the statuses from a local user
    return getLocalAccount(domain, db, handle, loadStats)
  }
  else if (handle.domain !== null) {
    // Retrieve the statuses of a remote actor
    const acct = `${handle.localPart}@${handle.domain}`
    return getRemoteAccount(handle, acct, db, loadStats)
  }
  else {
    return Promise.resolve(null)
  }
}

async function getRemoteAccount(handle: Handle, acct: string, db: D1Database, loadStats: boolean) {
  // TODO: using webfinger isn't the optimal implementation. We could cache
  // the object in D1 and directly query the remote API, indicated by the actor's
  // url field. For now, let's keep it simple.
  const actor = await queryAcct(handle.domain!, db, acct)
  if (actor === null) {
    return Promise.resolve(null)
  }
  return loadExternalMastodonAccount(acct, actor, loadStats)
}

async function getLocalAccount(domain: string, db: D1Database, handle: Handle, loadStats: boolean) {
  const actorId = actorURL(adjustLocalHostDomain(domain), handle.localPart)
  const actor = await getActorById(db, actorId)
  if (actor === null) {
    return Promise.resolve(null)
  }
  return loadLocalMastodonAccount(db, actor, loadStats)
}
