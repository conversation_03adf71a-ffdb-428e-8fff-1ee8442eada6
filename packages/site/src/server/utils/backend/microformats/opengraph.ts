import { parse, type Node, TEXT_NODE } from 'ultrahtml'
import { querySelectorAll } from 'ultrahtml/selector'
import { got } from '../http/got'
import type { mastodon } from '#shared/types'
import { decode } from '~/lib/html-entities'

// Some publications wrap their JSON-LD data in their <script> tags in commented-out CDATA blocks,
// they need to be removed before attempting to parse JSON
const CDATA_JUNK_PATTERN = /^\s*(\/\*\s*<!\[CDATA\[\s*\*)|(\/\/\s*<!\[CDATA\[)|(\/\*\s*\]\]>\s*\*\/)|(\/\/\s*\]\]>)\s*$/

function hostToUrl(value?: string): string | undefined {
  if (value) {
    return value?.startsWith('https://') || value?.startsWith('http://') ? value : `http://${value}`
  }
}

class StructuredData {
  constructor(private readonly data: Record<string, string>) {
  }

  get headline(): string | undefined {
    return this.data.headline
  }

  get description(): string | undefined {
    return this.data.description
  }

  get authorName(): string | undefined {
    return this.author?.name
  }

  get authorUrl(): string | undefined {
    return this.author?.url
  }

  get publisherName(): string | undefined {
    return this.publisher?.name
  }

  private get author(): Record<string, string> | undefined {
    const values = Array.isArray(this.data.author) ? this.data.author : [this.data.author]
    return values.shift()
  }

  private get publisher(): Record<string, string> | undefined {
    const values = Array.isArray(this.data.publisher) ? this.data.publisher : [this.data.publisher]
    return values.shift()
  }
}

class LinkDetails {
  private _document?: Node
  private _metaTags?: Node[]
  private _linkTags?: Node[]
  private _structuredData?: StructuredData

  constructor(private readonly originalUrl: string, private readonly body: string) {
  }

  toPreviewCard(): mastodon.v1.PreviewCard {
    return {
      url: this.url,
      title: this.title,
      description: this.description,
      type: this.type,
      authorName: this.authorName,
      authorUrl: this.authorUrl,
      html: this.html,
      providerName: this.providerName,
      providerUrl: this.providerUrl,
      width: this.width,
      height: this.height,
      image: this.image,
      embedUrl: this.embedUrl,
    }
  }

  get url(): string {
    return this.validUrl(this.linkTag('canonical') || this.opengraphTag('og:url'), true) || this.originalUrl
  }

  get type(): mastodon.v1.PreviewCardType {
    return this.playerUrl ? 'video' : 'link'
  }

  get title(): string {
    const value = this.structuredData?.headline || this.opengraphTag('og:title') || this.htmlTag('title')
    return value ? decode(value) : ''
  }

  get description(): string {
    const value = this.structuredData?.description || this.opengraphTag('og:description') || this.metaTag('description')
    return value ? decode(value) : ''
  }

  get providerName(): string | undefined {
    const value = this.structuredData?.publisherName || this.opengraphTag('og:site_name')
    return value && decode(value)
  }

  get providerUrl(): string | undefined {
    return this.validUrl(hostToUrl(this.opengraphTag('og:site')))
  }

  get authorName(): string | undefined {
    const value = this.structuredData?.authorName || this.opengraphTag('og:author') || this.opengraphTag('og:author:username')
    return value && decode(value)
  }

  get authorUrl(): string | undefined {
    return this.structuredData?.authorUrl
  }

  get embedUrl(): string {
    return this.validUrl(this.opengraphTag('twitter:player:stream')) ?? ''
  }

  get width(): number {
    return Number(this.opengraphTag('twitter:player:width') ?? 0)
  }

  get height(): number {
    return Number(this.opengraphTag('twitter:player:height') ?? 0)
  }

  get html(): string | undefined {
    return this.playerUrl && `<iframe src="${this.playerUrl}" width="${this.width}" height="${this.height}" allowfullscreen="true" allowtransparency="true" scrolling="no" frameborder="0"></iframe>`
  }

  get image(): string | undefined {
    return this.validUrl(this.opengraphTag('og:image'))
  }

  private get playerUrl(): string | undefined {
    return this.validUrl(this.opengraphTag('twitter:player'))
  }

  private validUrl(value: string | undefined, sameOrigin = false): string | undefined {
    if (!value) {
      return
    }
    const url = new URL(value, this.originalUrl)
    if (!url.host || !['http:', 'https:'].includes(url.protocol) || (sameOrigin && url.host != new URL(this.originalUrl).host)) {
      return
    }
    return url.href
  }

  private htmlTag(name: string): string | undefined {
    return querySelectorAll(this.document, name).shift()?.value
  }

  private linkTag(name: string): string | undefined {
    return this.linkTags
      .filter(({ attributes }) => attributes?.rel === name)
      .map(({ attributes }) => attributes?.href)
      .shift()
  }

  private metaTag(name: string): string | undefined {
    return this.metaTags.filter(({ attributes }) => attributes?.name === name).shift()?.value
  }

  private opengraphTag(name: string): string | undefined {
    return this.metaTags
      .filter(({ attributes }) => attributes?.name === name || attributes?.property === name)
      .map(({ attributes }) => attributes?.content)
      .shift()
  }

  private get structuredData(): StructuredData | undefined {
    // Some publications have more than one JSON-LD definition on the page,
    // and some of those definitions aren't valid JSON either, so we have
    // to loop through here until we find something that is the right type
    // and doesn't break
    if (!this._structuredData) {
      const jsonLd = Array.from(querySelectorAll(this.document, 'script'))
        .filter(({ attributes }) => attributes?.type === 'application/ld+json')
        .map(({ children }) => Array.from<Node>(children ?? []).find(el => el.type === TEXT_NODE)?.value)
        .map(content => content?.replace(CDATA_JUNK_PATTERN, ''))
        .map((content) => {
          try {
            return JSON.parse(content)
          }
          catch (err: any) {
            console.error(err)
          }
        })
        .filter(data => ['NewsArticle', 'WebPage'].includes(data?.['@type']))
        .shift()
      if (jsonLd === undefined) {
        return undefined
      }
      this._structuredData = new StructuredData(jsonLd)
    }
    return this._structuredData
  }

  private get linkTags(): Node[] {
    const tags = this._linkTags ? this._linkTags : querySelectorAll(this.document, 'link')
    this._linkTags = tags
    return tags.slice()
  }

  private get metaTags(): Node[] {
    const tags = this._metaTags ? this._metaTags : querySelectorAll(this.document, 'meta')
    this._metaTags = tags
    return tags.slice()
  }

  private get document(): Node {
    const document = this._document ? this._document : parse(this.body)
    this._document = document
    return document
  }
}

export async function opengraph(url: string): Promise<mastodon.v1.PreviewCard> {
  const data = await got<string>(url, 'text/html', res => res.text())
  return new LinkDetails(url, data).toPreviewCard()
}
