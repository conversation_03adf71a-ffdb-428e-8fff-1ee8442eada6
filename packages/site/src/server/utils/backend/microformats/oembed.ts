import { parse } from 'ultrahtml'
import { querySelectorAll } from 'ultrahtml/selector'
import type { Cache } from '../cache'
import type { mastodon } from '../types'
import { sanitizer, sanitizeHtml } from '../html/sanitize'
import { got } from '../http/got'

type OEmbedType = 'photo' | 'video' | 'link' | 'rich'
type OEmbedData = Record<string, string | number>
type OEmbedPreviewCard = Omit<mastodon.v1.PreviewCard, 'url'>

abstract class OEmbed {
  abstract type: OEmbedType
  title?: string
  author_name?: string
  author_url?: string
  provider_name?: string
  provider_url?: string
  cache_age?: number
  thumbnail_url?: string
  thumbnail_width?: number
  thumbnail_height?: number

  static async from(data: OEmbedData) {
    switch (data.type) {
      case 'photo':
        return new PhotoOEmbed(data)
      case 'video':
        return await VideoOEmbed.from(data)
      case 'link':
        return new LinkOEmbed(data)
    }
  }

  protected constructor(value: OEmbedData) {
    Object.assign(this, value)
  }

  toPreviewCard(): Omit<OEmbedPreviewCard, 'embedUrl'> {
    return {
      title: this.title ?? '',
      description: '',
      type: this.type,
      authorName: this.author_name,
      authorUrl: this.author_url,
      providerName: this.provider_name,
      providerUrl: this.provider_url,
      width: this.thumbnail_width,
      height: this.thumbnail_height,
      image: this.thumbnail_url,
    }
  }
}

class LinkOEmbed extends OEmbed {
  type: OEmbedType = 'link'
}

abstract class MediaOEmbed extends OEmbed {
  width: number
  height: number

  protected constructor({ width, height, ...value }: OEmbedData) {
    super(value)
    this.width = Number(width) ?? 0
    this.height = Number(height) ?? 0
  }

  override toPreviewCard(): Omit<OEmbedPreviewCard, 'embedUrl'> {
    return {
      ...super.toPreviewCard(),
      width: this.width,
      height: this.height,
    }
  }
}

class PhotoOEmbed extends MediaOEmbed {
  type: OEmbedType = 'photo'
  url: string

  constructor({ url, ...value }: OEmbedData) {
    super(value)
    this.url = `${url}`
  }

  override toPreviewCard(): OEmbedPreviewCard {
    return {
      ...super.toPreviewCard(),
      embedUrl: this.url,
    }
  }
}

class VideoOEmbed extends MediaOEmbed {
  type: OEmbedType = 'video'
  html: string

  static override async from({ html, ...value }: OEmbedData) {
    return new VideoOEmbed({
      html: await sanitizeHtml(`${html}`, [sanitizer.oembed]),
      ...value,
    })
  }

  constructor({ html, ...value }: OEmbedData) {
    super(value)
    this.html = `${html}`
  }

  override toPreviewCard(): Omit<OEmbedPreviewCard, 'embedUrl'> {
    return {
      ...super.toPreviewCard(),
      html: this.html,
    }
  }
}

function discover(content: string): URL | undefined {
  const node = querySelectorAll(parse(content), 'link')
    .filter(({ attributes }) => ['application/json+oembed', 'text/json+oembed'].includes(attributes?.type) && attributes?.href)
    .shift()
  if (node) {
    const href: string = node.attributes?.href
    const url = new URL(href)
    if (url.searchParams.has('url')) {
      url.searchParams.delete('url')
      return url
    }
  }
}

export async function oembed(url: string, cache: Cache): Promise<mastodon.v1.PreviewCard | undefined> {
  const domain = new URL(url).host
  const endpoint = await cache.fetch<string>(`oembed_endpoint:${domain}`, async () => {
    const baseUrl = discover(await got(url, 'text/html', res => res.text()))
    if (baseUrl) {
      // If the OEmbed endpoint is given as http but the URL we opened was served over https,
      // we can assume OEmbed will be available through https as well
      if (baseUrl.protocol !== 'https') {
        baseUrl.protocol = new URL(url).protocol
      }
      return baseUrl.href
    }
  })
  if (endpoint) {
    const baseUrl = new URL(endpoint)
    baseUrl.searchParams.set('url', url)
    const data = await got<OEmbedData>(baseUrl.href, 'application/json', res => res.json())
    const embed = await OEmbed.from(data)
    return embed && {
      url,
      embedUrl: '',
      ...embed?.toPreviewCard(),
    }
  }
}
