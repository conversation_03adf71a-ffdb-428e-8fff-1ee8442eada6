import type { Activity } from '~/server/utils/backend/activitypub/activities'
import { PUBLIC_GROUP } from '~/server/utils/backend/activitypub/activities'
import type { Actor } from '~/server/utils/backend/activitypub/actors'
import type { OrderedCollection } from '~/server/utils/backend/activitypub/objects/collection'
import { getMetadata, loadItems } from '~/server/utils/backend/activitypub/objects/collection'

export async function addObjectInOutbox(db: D1Database, actorId: string, objectId: string, published?: string, target?: string | string[]) {
  const targets = Array.isArray(target) ? target : (typeof target === 'string' ? [target] : [PUBLIC_GROUP])
  const batch = targets
    .map(target => ({
      id: crypto.randomUUID(),
      actor_id: actorId,
      object_id: objectId,
      ...(published ? { published_date: published } : {}),
      target,
    }))
    .map(binds => [Object.keys(binds), Object.values(binds)])
    .map(([keys, values]) => db.prepare(`INSERT INTO outbox_objects(${keys.join(',')}) VALUES(${Array(keys.length).fill('?')})`).bind(...values))

  const res = await db.batch(batch)
  for (const { success, error } of res) {
    if (!success) {
      throw new Error('SQL error: ' + error)
    }
  }
}

export async function removeObjectFromOutbox(db: D1Database, actorId: string, objectId: string) {
  const query = 'DELETE FROM actor_reblogs WHERE actor_id = ? AND object_id = ?'
  const { success, error } = await db.prepare(query).bind(actorId, objectId).run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

export async function get(actor: Actor): Promise<OrderedCollection<Activity>> {
  const collection = await getMetadata(actor.outbox)
  collection.items = await loadItems(collection, 20)

  return collection
}

export async function countStatuses(actor: Actor): Promise<number> {
  const metadata = await getMetadata(actor.outbox)
  return metadata.totalItems
}
