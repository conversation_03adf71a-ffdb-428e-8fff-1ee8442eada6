import { getTextContent, sanitizeContent, type APObject } from '../objects'
import { addPeer } from '~/server/utils/backend/activitypub/peers'
import { qb } from '~/server/utils/backend/database/querybuilder'
import type { mastodon } from '#shared/types'
import { generateUserKey } from '~/server/utils/backend/utils/key-ops'
import { defaultImages } from '~/server/utils/backend/config/accounts'
import { urlToHandle } from '~/server/utils/backend/utils/handle'
import { toMastodonAccount } from '~/server/utils/backend/mastodon/account'

export const PERSON = 'Person'
export const emailSymbol = Symbol()
export const isAdminSymbol = Symbol()
export const propertiesSymbol = Symbol()

interface ActorRow {
  id: string
  type: string
  email: string
  privkey: string
  privkey_salt: string
  pubkey: string
  cdate: string
  properties: string
}

export interface ActorInnerRow {
  actor_id: string
  actor_cdate: string
  actor_properties: string
}

export function actorURL(domain: string, id: string): URL {
  return new URL(`/ap/users/${id}`, 'https://' + domain)
}

// https://www.w3.org/TR/activitystreams-vocabulary/#actor-types
export interface Actor extends APObject {
  inbox: URL
  outbox: URL
  following: URL
  followers: URL

  alsoKnownAs?: string

  [emailSymbol]: string
  [isAdminSymbol]: boolean
  [propertiesSymbol]: PersonProperties
}

export type PersonProperties = {
  name?: string
  summary?: string
  icon?: { url: string }
  image?: { url: string }
  preferredUsername?: string
  cards?: mastodon.v1.TrendPreviewCard[]
  theme?: mastodon.v1.AccountTheme
  checksum?: string
  tags?: string[]

  inbox?: string
  outbox?: string
  following?: string
  followers?: string
}

// https://www.w3.org/TR/activitystreams-vocabulary/#dfn-person
export interface Person extends Actor {
  publicKey: {
    id: string
    owner: URL
    publicKeyPem: string
  }
}

export async function get(url: string | URL): Promise<Actor> {
  const headers = {
    accept: 'application/activity+json',
  }
  const res = await fetch(url.toString(), { headers })
  if (!res.ok) {
    throw new Error(`${url} returned: ${res.status}`)
  }

  const data = await res.json<any>()
  const actor: Actor = { ...data }
  actor.id = new URL(actor.id)

  if (actor.summary) {
    actor.summary = await sanitizeContent(actor.summary)
    if (actor.summary.length > 500) {
      actor.summary = actor.summary.substring(0, 500)
    }
  }
  if (actor.name) {
    actor.name = await getTextContent(actor.name)
    if (actor.name.length > 30) {
      actor.name = actor.name.substring(0, 30)
    }
  }
  if (actor.preferredUsername) {
    actor.preferredUsername = await getTextContent(actor.preferredUsername)
    if (actor.preferredUsername.length > 30) {
      actor.preferredUsername = actor.preferredUsername.substring(0, 30)
    }
  }

  // This is mostly for testing where for convenience not all values
  // are provided.
  // TODO: eventually clean that to better match production.
  if (actor.inbox !== undefined) {
    actor.inbox = new URL(actor.inbox)
  }
  if (actor.following !== undefined) {
    actor.following = new URL(actor.following)
  }
  if (actor.followers !== undefined) {
    actor.followers = new URL(actor.followers)
  }
  if (actor.outbox !== undefined) {
    actor.outbox = new URL(actor.outbox)
  }

  return actor
}

// Get and cache the Actor locally
export async function getAndCache(url: URL, db: D1Database): Promise<Actor> {
  {
    const actor = await getActorById(db, url)
    if (actor !== null) {
      return actor
    }
  }

  const actor = await get(url)
  if (!actor.type || !actor.id) {
    throw new Error('missing fields on Actor')
  }

  const properties = actor

  const sql = `
  INSERT INTO actors (id, type, properties)
  VALUES (?, ?, ?)
  `

  const { success, error } = await db
    .prepare(sql)
    .bind(actor.id.toString(), actor.type, JSON.stringify(properties))
    .run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }

  // Add peer
  {
    const domain = actor.id.host
    await addPeer(db, domain)
  }
  return actor
}

export async function getPersonByEmail(db: D1Database, email: string): Promise<Person | null> {
  const stmt = db.prepare('SELECT * FROM actors WHERE email=? AND type=?').bind(email, PERSON)
  const { results } = await stmt.all()
  if (!results || results.length === 0) {
    return null
  }
  const row: any = results[0]
  return personFromRow(row)
}

export async function getActorById(db: D1Database, id: URL): Promise<Person | null> {
  const stmt = db.prepare('SELECT * FROM actors WHERE id=?').bind(id.toString())
  const { results } = await stmt.all()
  if (!results || results.length === 0) {
    return null
  }
  const row: any = results[0]
  return personFromRow(row)
}

interface QueryActors {
  id?: string | URL
  ids?: Array<string | URL>
}

export async function queryActors(db: D1Database, { id, ids }: QueryActors) {
  const rules = []
  const binds = []
  if (ids) {
    rules.push('id IN (SELECT value FROM json_each(?))')
    binds.push(JSON.stringify(ids.map(id => typeof id === 'string' ? id : id.toString())))
  }
  if (id) {
    rules.push('id = ?')
    binds.push(typeof id === 'string' ? id : id.toString())
  }
  const where = rules.length ? `WHERE ${rules.join(' AND ')}` : ''
  const sql = `SELECT * FROM actors ${where}`
  const { results, success, error } = await db.prepare(sql).bind(...binds).all<ActorRow>()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  return results
}

export async function getActors(db: D1Database, { id, ids }: QueryActors) {
  const results = await queryActors(db, { id, ids })
  return results.map(personFromRow)
}

// Create a local user
export async function createPerson(
  domain: string,
  db: D1Database,
  userKEK: string,
  email: string,
  properties: PersonProperties = {},
  admin: boolean = false,
): Promise<Person> {
  const userKeyPair = await generateUserKey(userKEK)

  const privkey = [...new Uint8Array(userKeyPair.wrappedPrivKey)]
  const salt = [...new Uint8Array(userKeyPair.salt)]

  if (properties.preferredUsername === undefined) {
    const parts = email.split('@')
    properties.preferredUsername = parts[0]
  }

  if (properties.preferredUsername !== undefined && typeof properties.preferredUsername !== 'string') {
    throw new Error(
      `preferredUsername should be a string, received ${JSON.stringify(properties.preferredUsername)} instead`,
    )
  }

  const id = actorURL(domain, properties.preferredUsername).toString()

  if (properties.inbox === undefined) {
    properties.inbox = id + '/inbox'
  }

  if (properties.outbox === undefined) {
    properties.outbox = id + '/outbox'
  }

  if (properties.following === undefined) {
    properties.following = id + '/following'
  }

  if (properties.followers === undefined) {
    properties.followers = id + '/followers'
  }

  const row = await db
    .prepare(
      `
              INSERT INTO actors(id, type, email, pubkey, privkey, privkey_salt, properties, is_admin)
              VALUES(?, ?, ?, ?, ?, ?, ?, ?)
              RETURNING *
          `,
    )
    .bind(id, PERSON, email, userKeyPair.pubKey, privkey, salt, JSON.stringify(properties), admin ? 1 : null)
    .first()

  return personFromRow(row)
}

export async function updateActor(db: D1Database, properties: PersonProperties, id: URL) {
  const { success, error } = await db
    .prepare('UPDATE actors SET properties = ? WHERE id = ?')
    .bind(JSON.stringify(properties), id.toString())
    .run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

export async function updateActorProperty(db: D1Database, actorId: URL, key: string, value: string) {
  const { success, error } = await db
    .prepare(`UPDATE actors SET properties=${qb.jsonSet('properties', key, '?1')} WHERE id=?2`)
    .bind(value, actorId.toString())
    .run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

export async function setActorAlias(db: D1Database, actorId: URL, alias: URL) {
  const { success, error } = await db
    .prepare(
      `UPDATE actors SET properties=${qb.jsonSet('properties', 'alsoKnownAs', 'json_array(?1)')} WHERE id=?2`,
    )
    .bind(alias.toString(), actorId.toString())
    .run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

export async function deleteActor(db: D1Database, id: string) {
  const batch = [
    db.prepare('DELETE FROM actor_conversations WHERE actor_id=?').bind(id),
    db.prepare('DELETE FROM actor_favourites WHERE actor_id=?').bind(id),
    db.prepare('DELETE FROM actor_following_actors WHERE actor_id=?1 OR target_actor_id=?1').bind(id),
    db.prepare('DELETE FROM actor_following_tags WHERE actor_id=?').bind(id),
    db.prepare('DELETE FROM actor_notifications WHERE actor_id=?1 OR from_actor_id=?1').bind(id),
    db.prepare('DELETE FROM actor_reblogs WHERE actor_id=?').bind(id),
    db.prepare('DELETE FROM actor_replies WHERE actor_id=?').bind(id),
    db.prepare('DELETE FROM actors WHERE id=?').bind(id),
  ]
  const res = await db.batch(batch)
  for (let i = 0, len = res.length; i < len; i++) {
    if (!res[i].success) {
      throw new Error('SQL error: ' + res[i].error)
    }
  }
}

export function actorFromInnerRow(row: ActorInnerRow) {
  return personFromRow({
    id: row.actor_id,
    cdate: row.actor_cdate,
    properties: row.actor_properties,
  })
}

export function personFromRow(row: any) {
  const properties = JSON.parse(row.properties) as PersonProperties

  const icon = properties.icon ?? {
    type: 'Image',
    mediaType: 'image/jpeg',
    url: new URL(defaultImages.avatar),
    id: new URL(row.id + '#icon'),
  }
  const image = properties.image ?? {
    type: 'Image',
    mediaType: 'image/jpeg',
    url: new URL(defaultImages.header),
    id: new URL(row.id + '#image'),
  }

  const preferredUsername = properties.preferredUsername
  const name = properties.name ?? preferredUsername

  let publicKey = null
  if (row.pubkey !== null) {
    publicKey = {
      id: row.id + '#main-key',
      owner: row.id,
      publicKeyPem: row.pubkey,
    }
  }

  const id = new URL(row.id)

  let domain = id.hostname
  if (row.original_actor_id) {
    domain = new URL(row.original_actor_id).hostname
  }

  // Old local actors weren't created with inbox/outbox/etc properties, so add
  // them if missing.
  {
    if (properties.inbox === undefined) {
      properties.inbox = id + '/inbox'
    }

    if (properties.outbox === undefined) {
      properties.outbox = id + '/outbox'
    }

    if (properties.following === undefined) {
      properties.following = id + '/following'
    }

    if (properties.followers === undefined) {
      properties.followers = id + '/followers'
    }
  }

  return {
    // Hidden values
    [emailSymbol]: row.email,
    [isAdminSymbol]: row.is_admin === 1,
    [propertiesSymbol]: properties,

    ...properties,
    name,
    icon,
    image,
    preferredUsername,
    discoverable: true,
    publicKey,
    type: PERSON,
    id,
    published: new Date(row.cdate).toISOString(),

    url: new URL('@' + preferredUsername, 'https://' + domain),
  } as unknown as Person
}

export function toAdminAccount(row: ActorRow): mastodon.v1.Admin.Account {
  const account = personFromRow(row)
  const { preferredUsername }: PersonProperties = JSON.parse(row.properties)
  return {
    id: row.id,
    username: preferredUsername ?? '',
    createdAt: row.cdate,
    email: row.email,
    ips: [],
    locale: '',
    role: {
      id: 0,
      name: '',
      color: '',
      position: 0,
      permissions: 0,
      highlighted: false,
      createdAt: '',
      updatedAt: '',
    },
    confirmed: false,
    approved: false,
    disabled: false,
    silenced: false,
    suspended: false,
    sensitized: false,
    account: toMastodonAccount(urlToHandle(account.id), account),
  }
}
