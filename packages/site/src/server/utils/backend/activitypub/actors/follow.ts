import type { Actor } from '~/server/utils/backend/activitypub/actors'
import * as actors from '~/server/utils/backend/activitypub/actors'
import type { OrderedCollection } from '~/server/utils/backend/activitypub/objects/collection'
import { getMetadata, loadItems } from '~/server/utils/backend/activitypub/objects/collection'

export async function countFollowing(actor: Actor): Promise<number> {
  const collection = await getMetadata(actor.following)
  return collection.totalItems
}

export async function countFollowers(actor: Actor): Promise<number> {
  const collection = await getMetadata(actor.followers)
  return collection.totalItems
}

export async function getFollowers(actor: Actor): Promise<OrderedCollection<string>> {
  const collection = await getMetadata(actor.followers)
  collection.items = await loadItems<string>(collection)
  return collection
}

export async function getFollowing(actor: Actor): Promise<OrderedCollection<string>> {
  const collection = await getMetadata(actor.following)
  collection.items = await loadItems<string>(collection)
  return collection
}

export async function loadActors(db: D1Database, collection: OrderedCollection<string>): Promise<Array<Actor>> {
  const promises = collection.items.map((item) => {
    const actorId = new URL(item)
    return actors.getAndCache(actorId, db)
  })

  return Promise.all(promises)
}
