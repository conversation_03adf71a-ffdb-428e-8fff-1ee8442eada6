import { getResultsField } from '~/server/utils/backend/mastodon/utils'
import { qb } from '~/server/utils/backend/database/querybuilder'

export async function getPeers(db: D1Database): Promise<Array<string>> {
  const query = `SELECT domain FROM peers `
  const statement = db.prepare(query)

  return getResultsField(statement, 'domain')
}

export async function addPeer(db: D1Database, domain: string): Promise<void> {
  const query = qb.insertOrIgnore('INTO peers (domain) VALUES (?)')

  const out = await db.prepare(query).bind(domain).run()
  if (!out.success) {
    throw new Error('SQL error: ' + out.error)
  }
}
