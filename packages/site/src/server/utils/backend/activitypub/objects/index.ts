import { addPeer } from '~/server/utils/backend/activitypub/peers'
import { qb } from '~/server/utils/backend/database/querybuilder'
import type { UUID } from '#shared/types'

export const IMAGE = 'Image'

export const originalActorIdSymbol = Symbol()
export const originalObjectIdSymbol = Symbol()
export const mastodonIdSymbol = Symbol()
export const propertiesSymbol = Symbol()

export interface APObjectRow {
  id: string
  mastodon_id: UUID
  type: string
  cdate: string
  original_actor_id: string
  original_object_id: string
  reply_to_object_id: string
  properties: string
  local: number
}

export interface APObject {
  type: string
  // ObjectId, URL used for federation. Called `uri` in Mastodon APIs.
  // https://www.w3.org/TR/activitypub/#obj-id
  id: URL
  // Link to the HTML representation of the object
  url: URL
  published?: string
  icon?: APObject
  image?: APObject
  summary?: string
  name?: string
  mediaType?: string
  content?: string
  inReplyTo?: string

  // Extension
  preferredUsername?: string
  // Internal
  [originalActorIdSymbol]?: string
  [originalObjectIdSymbol]?: string
  [mastodonIdSymbol]?: UUID
  [propertiesSymbol]?: any
}

// https://www.w3.org/TR/activitystreams-vocabulary/#dfn-document
export interface Document extends APObject { }

// https://www.w3.org/TR/activitystreams-vocabulary/#dfn-image
export interface Image extends Document {
  description?: string
}

export function uri(domain: string, id: string): URL {
  return new URL('/ap/o/' + id, 'https://' + domain)
}

export async function createObject<T extends APObject>(
  domain: string,
  db: D1Database,
  type: string,
  properties: any,
  originalActorId: URL,
  local: boolean,
  uuid: string = crypto.randomUUID(),
): Promise<T> {
  const sanitizedProperties = await sanitizeObjectProperties(properties)
  const data: Record<string, string | number | null> = {
    id: uri(domain, uuid).toString(),
    mastodon_id: uuid,
    type,
    original_actor_id: originalActorId.toString(),
    original_object_id: null,
    reply_to_object_id: properties.inReplyTo ?? null,
    properties: JSON.stringify(sanitizedProperties),
    local: local ? 1 : 0,
  }
  const sql = `INSERT INTO objects(${Object.keys(data).join(', ')}) VALUES(${Array(Object.keys(data).length).fill('?').join(', ')}) RETURNING *`
  const binds = Object.values(data)
  console.debug(sql)
  console.debug(binds)
  const row: any = await db.prepare(sql).bind(...binds).first()

  return {
    ...sanitizedProperties,
    type,
    id: new URL(row.id),
    published: new Date(row.cdate).toISOString(),

    [mastodonIdSymbol]: row.mastodon_id,
    [originalActorIdSymbol]: row.original_actor_id,
    [propertiesSymbol]: JSON.parse(row.properties),
  } as T
}

export async function get<T>(url: URL): Promise<T> {
  const headers = {
    accept: 'application/activity+json',
  }
  const res = await fetch(url, { headers })
  if (!res.ok) {
    throw new Error(`${url} returned: ${res.status}`)
  }

  return res.json<T>()
}

type CacheObjectRes = {
  created: boolean
  object: APObject
}

export async function cacheObject(
  domain: string,
  db: D1Database,
  properties: unknown,
  originalActorId: URL,
  originalObjectId: URL,
  local: boolean,
): Promise<CacheObjectRes> {
  const cachedObject = await getObjectBy(db, { originalObjectId })
  if (cachedObject !== null) {
    return {
      created: false,
      object: cachedObject,
    }
  }

  const uuid = crypto.randomUUID()
  const apId = uri(domain, uuid).toString()
  const sanitizedProperties = await sanitizeObjectProperties(properties)

  const row: any = await db
    .prepare('INSERT INTO objects(id, type, properties, original_actor_id, original_object_id, local, mastodon_id) VALUES(?, ?, ?, ?, ?, ?, ?) RETURNING *',).bind(
      apId,
      sanitizedProperties.type,
      JSON.stringify(sanitizedProperties),
      originalActorId.toString(),
      originalObjectId.toString(),
      local ? 1 : 0,
      uuid,
    )
    .first()

  // Add peer
  {
    const domain = originalObjectId.host
    await addPeer(db, domain)
  }

  {
    const properties = JSON.parse(row.properties)
    const object = {
      published: new Date(row.cdate).toISOString(),
      ...properties,

      type: row.type,
      id: new URL(row.id),

      [mastodonIdSymbol]: row.mastodon_id,
      [originalActorIdSymbol]: row.original_actor_id,
      [originalObjectIdSymbol]: row.original_object_id,
      [propertiesSymbol]: properties,
    } as APObject

    return { object, created: true }
  }
}

export async function updateObject(db: D1Database, obj: APObject, properties: any) {
  const { success, error } = await db
    .prepare('UPDATE objects SET properties = ? WHERE id = ?')
    .bind(JSON.stringify(properties), obj.id.toString())
    .run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

export async function updateObjectProperty(db: D1Database, obj: APObject, key: string, value: string) {
  const { success, error } = await db
    .prepare(`UPDATE objects SET properties=${qb.jsonSet('properties', key, '?1')} WHERE id=?2`)
    .bind(value, obj.id.toString())
    .run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

export async function getObjectById(db: D1Database, id: string | URL): Promise<APObject | null> {
  return getObjectBy(db, { id })
}

export async function getObjectByOriginalId(db: D1Database, originalObjectId: string | URL): Promise<APObject | null> {
  return getObjectBy(db, { originalObjectId })
}

export async function getObjectByMastodonId(db: D1Database, mastodonId: UUID): Promise<APObject | null> {
  return getObjectBy(db, { mastodonId })
}

interface QueryObjects {
  id?: string | URL
  ids?: Array<string | URL>
  originalObjectId?: string | URL
  actorId?: string | URL
  mastodonId?: UUID
  mastodonIds?: UUID[]
  conversationId?: UUID
  target?: string
  targets?: string[]
  type?: string
  notReplyTo?: true
}

export async function getObjectBy<T extends APObject = APObject>(db: D1Database, query: QueryObjects) {
  const results = await getObjectsBy(db, query)
  if (!results || results.length === 0) {
    return null
  }
  return results[0] as T
}

export async function getObjectsBy(db: D1Database, { id, ids, originalObjectId, actorId, mastodonId, mastodonIds, conversationId, target, targets, type, notReplyTo }: QueryObjects) {
  const rules = []
  const binds = []
  if (id) {
    rules.push('id = ?')
    binds.push(typeof id === 'string' ? id : id.toString())
  }
  if (ids) {
    rules.push('id IN (SELECT value FROM json_each(?))')
    binds.push(JSON.stringify(ids.map(id => typeof id === 'string' ? id : id.toString())))
  }
  if (originalObjectId) {
    rules.push('original_object_id = ?')
    binds.push(typeof originalObjectId === 'string' ? originalObjectId : originalObjectId.toString())
  }
  if (actorId) {
    rules.push('original_actor_id = ?')
    binds.push(typeof actorId === 'string' ? actorId : actorId.toString())
  }
  if (mastodonId) {
    rules.push('mastodon_id = ?')
    binds.push(mastodonId)
  }
  if (mastodonIds) {
    rules.push('mastodon_id IN (SELECT value FROM json_each(?))')
    binds.push(JSON.stringify(mastodonIds))
  }
  if (conversationId) {
    rules.push('json_extract(properties, \'$.conversation_id\') = ?')
    binds.push(conversationId)
  }
  if (notReplyTo) {
    rules.push('json_extract(objects.properties, \'$.inReplyTo\') IS NULL')
  }
  if (targets) {
    rules.push('target IN (SELECT value FROM json_each(?))')
    binds.push(JSON.stringify(targets))
  }
  if (target) {
    rules.push('target = ?')
    binds.push(target)
  }
  if (type) {
    rules.push('type = ?')
    binds.push(type)
  }
  const where = rules.length ? `WHERE ${rules.join(' AND ')}` : ''
  const query = `SELECT * FROM objects ${where}`
  console.debug(query)
  console.debug(binds)
  const { results, success, error } = await db.prepare(query).bind(...binds).all()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  return results.map(toObjectFromRow)
}

export function toObjectFromRow(result: any): APObject {
  const properties = JSON.parse(result.properties)
  return {
    published: new Date(result.cdate).toISOString(),
    ...properties,

    type: result.type,
    id: new URL(result.id),

    [mastodonIdSymbol]: result.mastodon_id,
    [originalActorIdSymbol]: result.original_actor_id,
    [originalObjectIdSymbol]: result.original_object_id,
    [propertiesSymbol]: properties,
  }
}

/** Is the given `value` an ActivityPub Object? */
function isAPObject(value: unknown): value is APObject {
  return value !== null && typeof value === 'object'
}

/** Sanitizes the ActivityPub Object `properties` prior to being stored in the DB. */
export async function sanitizeObjectProperties(value: unknown): Promise<APObject> {
  if (!isAPObject(value)) {
    throw new Error('Invalid object properties. Expected an object but got ' + JSON.stringify(value))
  }
  const properties: APObject = {
    ...value,
  }
  // if ('content' in properties) {
  // 	sanitized.content = await sanitizeContent(properties.content as string)
  // }
  if (properties.name) {
    properties.name = await getTextContent(properties.name)
  }
  return properties
}

/**
 * Sanitizes the given string as ActivityPub Object content.
 *
 * This sanitization follows that of Mastodon
 *  - convert all elements to `<p>` unless they are recognized as one of `<p>`, `<span>`, `<br>` or `<a>`.
 *  - remove all CSS classes that are not micro-formats or semantic.
 *
 * See https://docs.joinmastodon.org/spec/activitypub/#sanitization
 */
export async function sanitizeContent(unsafeContent: string): Promise<string> {
  return await getContentRewriter().transform(new Response(unsafeContent)).text()
}

/**
 * This method removes all HTML elements from the string leaving only the text content.
 */
export async function getTextContent(unsafeName: string) {
  return await getTextContentRewriter().transform(new Response(unsafeName)).text()
}

function getContentRewriter() {
  const contentRewriter = new HTMLRewriter()
  contentRewriter.on('*', {
    element(el) {
      if (!['p', 'span', 'br', 'a'].includes(el.tagName)) {
        const element = el as { tagName: string }
        element.tagName = 'p'
      }

      if (el.hasAttribute('class')) {
        const classes = el.getAttribute('class')!.split(/\s+/)
        const sanitizedClasses = classes.filter(c =>
          /^(h|p|u|dt|e)-|^mention$|^hashtag$|^ellipsis$|^invisible$/.test(c),
        )
        el.setAttribute('class', sanitizedClasses.join(' '))
      }
    },
  })
  return contentRewriter
}

function getTextContentRewriter() {
  const rewriter = new HTMLRewriter()
  rewriter.on('*', {
    element(el) {
      el.removeAndKeepContent()
      if (['p', 'br'].includes(el.tagName)) {
        el.after(' ')
      }
    },
  })
  return rewriter
}

// TODO: eventually use SQLite's `ON DELETE CASCADE` but requires writing the DB
// schema directly into D1, which D1 disallows at the moment.
// Some context at: https://stackoverflow.com/questions/13150075/add-on-delete-cascade-behavior-to-an-sqlite3-table-after-it-has-been-created
export async function deleteObject(db: D1Database, obj: APObject) {
  const objectId = obj.id.toString()
  const batch = [
    db.prepare('DELETE FROM outbox_objects WHERE object_id=?').bind(objectId),
    db.prepare('DELETE FROM inbox_objects WHERE object_id=?').bind(objectId),
    db.prepare('DELETE FROM actor_notifications WHERE object_id=?').bind(objectId),
    db.prepare('DELETE FROM actor_favourites WHERE object_id=?').bind(objectId),
    db.prepare('DELETE FROM actor_reblogs WHERE object_id=?').bind(objectId),
    db.prepare('DELETE FROM actor_replies WHERE object_id=?1 OR in_reply_to_object_id=?1').bind(objectId),
    db.prepare('DELETE FROM idempotency_keys WHERE object_id=?').bind(objectId),
    db.prepare('DELETE FROM note_tags WHERE object_id=?').bind(objectId),
    db.prepare('DELETE FROM poll_votes WHERE poll_id IN (SELECT id FROM polls WHERE object_id=?)').bind(objectId),
    db.prepare('DELETE FROM polls WHERE object_id=?').bind(objectId),
    db.prepare('DELETE FROM objects WHERE id=?').bind(objectId),
  ]
  const res = await db.batch(batch)
  for (let i = 0, len = res.length; i < len; i++) {
    if (!res[i].success) {
      throw new Error('SQL error: ' + res[i].error)
    }
  }
}
