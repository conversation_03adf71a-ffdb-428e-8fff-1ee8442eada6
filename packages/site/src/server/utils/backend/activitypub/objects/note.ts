// https://www.w3.org/TR/activitystreams-vocabulary/#object-types

import { Remarkable } from 'remarkable'
import type { Actor } from '../actors'
import { propertiesSymbol } from '.'
import type { mastodon } from '../../types'
import type { Link } from './link'
import { createObject, updateObject, type APObject, deleteObject } from '.'
import { htmlToText } from '~/server/utils/backend/utils/content-parse'
import { sanitize } from '~/server/utils/backend/content/sanitize'

export const NOTE = 'Note'

export interface NoteProperties {
  spoiler_text?: string
  content?: string
  text?: string
  card: mastodon.v1.PreviewCard
  conversation_id?: string
  updated?: string
}

// https://www.w3.org/TR/activitystreams-vocabulary/#dfn-note
export interface Note extends APObject {
  content: string
  attributedTo?: string
  summary?: string
  inReplyTo?: string
  replies?: string
  to: Array<string>
  attachment: Array<APObject>
  cc: Array<string>
  tag: Array<Link>
  spoiler_text?: string
}

export async function createNote(
  domain: string,
  db: D1Database,
  content: string,
  actor: Actor,
  to: string[],
  cc: string[],
  attachment: Array<APObject> = [],
  { gridSize, ...extraProperties }: any = {},
): Promise<Note> {
  const { html, text, img, wave } = await sanitize(content)

  const properties = {
    attributedTo: actor.id.toString(),
    content: html,
    text,
    to,
    cc,

    // FIXME: stub values
    inReplyTo: null,
    replies: null,
    sensitive: false,
    summary: null,
    tag: [],
    attachment,

    card: await createPreviewCard(html, text, img, wave, gridSize),

    ...extraProperties,
  }
  return createObject(domain, db, NOTE, properties, actor.id, true)
}

export async function updateNote(db: D1Database, obj: APObject, content?: string, gridSize?: mastodon.v1.PreviewCardGridSize | null) {
  const media: string[] = []
  const properties: NoteProperties = obj[propertiesSymbol]
  if (!gridSize) {
    gridSize = properties.card.gridSize
  }
  if (content !== undefined) {
    const { html, text, img, mediaIds, wave } = await sanitize(content)
    if (properties.content) {
      const info = await sanitize(properties.content)
      for (const mediaId of info.mediaIds) {
        if (mediaIds.includes(mediaId) === false) {
          media.push(mediaId)
        }
      }
    }
    properties.content = html
    properties.text = text
    properties.card = await createPreviewCard(html, text, img, wave, gridSize)
  }
  else {
    properties.card.gridSize = gridSize
  }
  await updateObject(db, obj, properties)
  return media
}

export async function deleteNote(db: D1Database, obj: APObject) {
  const media: string[] = []
  const properties: NoteProperties = obj[propertiesSymbol]
  if (properties.content) {
    const info = await sanitize(properties.content)
    for (const mediaId of info.mediaIds) {
      media.push(mediaId)
    }
  }
  await deleteObject(db, obj)
  return media
}

export async function createPreviewCard(html: string, text: string, image: string | null, wave: number[] | undefined, gridSize?: mastodon.v1.PreviewCardGridSize | null): Promise<mastodon.v1.PreviewCard> {
  const markdown = cleanAndTrimMarkdown(htmlToText(html), 500)
  const description = new Remarkable().render(markdown) // .replace(/<a\s+href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '<span>$2</span>')
  let type: mastodon.v1.PreviewCardType = 'text'
  if (image || wave) {
    if (text) {
      type = 'rich'
    }
    else {
      type = 'media'
    }
  }
  return {
    url: '',
    title: '',
    description,
    type,
    embedUrl: '',
    ...(gridSize === null ? {} : { gridSize }),
    ...(image === null ? {} : { image }),
    ...(wave === undefined ? {} : { wave }),
  }
}

function cleanAndTrimMarkdown(markdown: string, length: number) {
  markdown = removeImagesFromMarkdown(markdown)
  markdown = removeLinksFromMarkdown(markdown)
  let trimmed = ''
  const paragraphs = markdown.split(/\n/)
  for (const paragraph of paragraphs) {
    if (trimmed.length >= length) {
      break
    }
    trimmed += '\n'
    const words = paragraph.split(/\s+/)
    for (const word of words) {
      if (trimmed.length + words.length >= length) {
        break
      }
      if (word.startsWith('#')) {
        trimmed += word
      }
      else {
        trimmed += ` ${word}`
      }
    }
  }
  return trimmed
}

function removeImagesFromMarkdown(markdown: string) {
  return markdown.replace(/!\[.*?\]\(.*?\)/g, '')
}

function removeLinksFromMarkdown(markdown: string) {
  return markdown.replace(/\[([^\]]*)\]\([^)]*\)/g, '$1')
}
