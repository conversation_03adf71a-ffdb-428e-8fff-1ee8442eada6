import { HTTPException } from 'hono/http-exception'
import type { Actor } from '../activitypub/actors'
import { getObjectByMastodonId } from '../activitypub/objects'
import type { UUID, mastodon } from '../types'

interface PollRow {
  object_id: UUID
}

interface VoteData {
  actor_id: string
  poll_id: string
  choice: number
}

export async function vote(db: D1Database, actor: Actor, pollId: UUID, choices: number[]) {
  const objectId = (await getPoll(db, pollId))?.object_id
  if (!objectId) {
    throw new HTTPException(404)
  }
  const obj = await getObjectByMastodonId(db, objectId)
  if (!obj) {
    throw new HTTPException(404)
  }
  const poll = (obj as unknown as { polls?: mastodon.v1.Poll[] }).polls?.find(({ id }) => id === pollId)
  if (!poll) {
    throw new HTTPException(404)
  }
  if (poll.expiresAt) {
    const skew = Math.abs(new Date().getTime() - new Date(poll.expiresAt).getTime())
    if (skew > 0) {
      throw new HTTPException(422, { message: 'Validation failed: The poll has already ended' })
    }
  }
  if (!poll.multiple && choices.length > 1) {
    throw new HTTPException(422, { message: 'Validation failed' })
  }
  const votes = await getVotes(db, pollId, actor)
  if (votes.length > 0) {
    throw new HTTPException(422, { message: 'Validation failed: You have already voted on this poll' })
  }

  await setVotes(db, actor, pollId, choices)

  return {
    ...poll,
    voted: true,
    ownVotes: choices,
  }
}

export async function getPoll(db: D1Database, id: UUID): Promise<PollRow | null> {
  const query = 'SELECT * FROM polls WHERE id=?'
  const { results, success, error } = await db.prepare(query).bind(id).all<PollRow>()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results || results.length === 0) {
    return null
  }
  return results[0]
}

export async function getVotes(db: D1Database, id: UUID, actor: Actor): Promise<VoteData[]> {
  const query = 'SELECT * FROM poll_votes WHERE actor_id=? AND poll_id=?'
  const { results, success, error } = await db.prepare(query).bind(actor.id.toString(), id).all<VoteData>()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  return results ?? []
}

async function setVotes(db: D1Database, actor: Actor, pollId: UUID, choices: number[]) {
  const query = 'INSERT INTO poll_votes (actor_id, poll_id, choice) VALUES (?, ?, ?)'
  const stmt = db.prepare(query)
  for (const choice of choices) {
    const { success, error } = await stmt.bind(actor.id.toString(), pollId, choice).run()
    if (!success) {
      throw new Error('SQL error: ' + error)
    }
  }
}
