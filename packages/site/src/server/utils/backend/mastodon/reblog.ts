// Also known as boost.

import { addObjectInOutbox, removeObjectFromOutbox } from '../activitypub/actors/outbox'
import { getResultsField } from './utils'
import type { APObject } from '~/server/utils/backend/activitypub/objects'
import type { Actor } from '~/server/utils/backend/activitypub/actors'

/**
 * Creates a reblog and inserts it in the reblog author's outbox
 */
export async function createReblog(db: D1Database, actor: Actor, objectId: string) {
  await Promise.all([
    addObjectInOutbox(db, actor.id.toString(), objectId),
    insertReblog(db, actor, objectId),
  ])
}

export async function removeReblog(db: D1Database, actor: Actor, objectId: string) {
  await Promise.all([
    removeObjectFromOutbox(db, actor.id.toString(), objectId),
    deleteReblog(db, actor, objectId),
  ])
}

async function deleteReblog(db: D1Database, actor: Actor, objectId: string) {
  const query = 'DELETE FROM actor_reblogs WHERE actor_id = ? AND object_id = ?'
  const { success, error } = await db.prepare(query).bind(actor.id.toString(), objectId).run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

async function insertReblog(db: D1Database, actor: Actor, objectId: string) {
  const query = 'INSERT INTO actor_reblogs (id, actor_id, object_id) VALUES (?, ?, ?)'
  const { success, error } = await db.prepare(query).bind(crypto.randomUUID(), actor.id.toString(), objectId).run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

export async function countReblogs(db: D1Database, objectId: string): Promise<number> {
  const query = 'SELECT count(*) as count FROM actor_reblogs WHERE object_id=?'
  const out = await db.prepare(query).bind(objectId).first<{ count: number }>()
  return out?.count ?? 0
}

export async function hasReblog(db: D1Database, actor: Actor, obj: APObject): Promise<boolean> {
  const query = 'SELECT count(*) as count FROM actor_reblogs WHERE object_id=? AND actor_id=?'
  const out = await db.prepare(query).bind(obj.id.toString(), actor.id.toString()).first<{ count: number }>()
  return out !== null && out.count > 0
}
