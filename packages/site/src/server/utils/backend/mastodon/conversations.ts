import { actorURL, getActorById, type Actor } from '../activitypub/actors'
import { createActorConversations, deleteActorConversations, getActorConversationsBy, updateActorConversations, type ActorConversationProperties } from '../entities/conversation/actors'
import { createConversationMessage, deleteConversationMessage, getConversationMessagesBy, updateConversationMessage } from '../entities/conversation/message'
import type { ConversationTopic } from '../entities/conversation/topic'
import { createConversationTopic, deleteConversationTopic, getConversationTopic, updateConversationTopic } from '../entities/conversation/topic'
import type { Backblaze } from '../media/backblaze'
import { urlToHandle } from '../utils/handle'
import { isLocalHandle, parseHandle } from '../utils/parse'
import { wsPublish } from '../ws/stream'
import { toMastodonAccount } from './account'
import type { ConversationMessage, mastodon } from '~/shared/types'
import { sanitize } from '~/server/utils/backend/content/sanitize'

export async function getConversations(db: D1Database, actorId: string): Promise<mastodon.v1.Conversation[]> {
  const results = await getActorConversationsBy(db, { participantId: actorId })
  return Object.values(results.reduce<Record<string, mastodon.v1.Conversation>>((result, { conversationId, actor, properties: { unread } }) => {
    const conversation: mastodon.v1.Conversation = result[conversationId] ?? { id: conversationId, accounts: [], unread: false }
    if (actorId === actor.id.toString()) {
      conversation.unread = Boolean(unread)
    }
    conversation.accounts.push(toMastodonAccount(urlToHandle(actor.id), actor))

    return { ...result, [conversationId]: conversation }
  }, {}))
}

export async function deleteConversation(db: D1Database, actorId: string, topicId: string) {
  const [actorConversation] = await getActorConversationsBy(db, { topicId, actorId })
  if (actorConversation) {
    await deleteActorConversations(db, actorConversation.id)
    const rows = await getActorConversationsBy(db, { topicId })
    if (rows.length === 0) {
      const messages = await getConversationMessagesBy(db, { topicId })
      for (const message of messages) {
        await deleteConversationMessage(db, message.id)
      }
      await deleteConversationTopic(db, topicId)
    }
  }
}

export async function newConversation(db: D1Database, domain: string, actor: Actor, key?: string | null) {
  if (!key) {
    return
  }
  let conversation: ConversationTopic | null = null
  const [actorConversation] = await getActorConversationsBy(db, { topicId: key, actorId: actor.id.toString() })
  if (actorConversation) {
    conversation = await getConversationTopic(db, actorConversation.conversationId)
  }
  if (!conversation) {
    const handle = parseHandle(key)
    if (!isLocalHandle(handle, domain)) {
      return
    }
    const recipient = await getActorById(db, actorURL(domain, handle.localPart))
    if (!recipient || recipient.id.toString() === actor.id.toString()) {
      return
    }

    const conversations = await getActorConversationsBy(db, { actorIds: [actor.id.toString(), recipient.id.toString()] })
    if (conversations.length) {
      const actorConversationIds = conversations.filter(({ actorId }) => actorId === actor.id.toString()).map(({ conversationId }) => conversationId)
      const recipientConversationIds = conversations.filter(({ actorId }) => actorId === recipient.id.toString()).map(({ conversationId }) => conversationId)
      const conversationIds = recipientConversationIds.filter(conversationId => actorConversationIds.includes(conversationId))
      for (const conversationId of conversationIds) {
        const rows = conversations.filter(({ id }) => id === conversationId)
        if (rows.length === 2) {
          conversation = await getConversationTopic(db, conversationId)
          break
        }
      }
    }
    if (!conversation) {
      conversation = await createConversationTopic(db)
      if (conversation) {
        await createActorConversations(db, conversation.id, [actor.id.toString(), recipient.id.toString()])
      }
    }
  }

  if (conversation) {
    const actorConversations = await getActorConversationsBy(db, { topicId: conversation.id, actorId: actor.id.toString() })
    if (actorConversations.length === 0) {
      await createActorConversations(db, conversation.id, [actor.id.toString()])
    }
    return conversation.id
  }
}

export async function addMessageToConversation(db: D1Database, wsService: Service, topicId: string | undefined, actor: Actor, content: string, publishId: string) {
  if (!topicId) {
    return
  }
  const topic = await getConversationTopic(db, topicId)
  if (!topic) {
    return
  }

  const { html } = await sanitize(content)
  const row = await createConversationMessage(db, topicId, publishId, html, actor)
  if (!row) {
    return
  }
  const message: ConversationMessage = {
    id: row.id,
    conversationId: row.conversationId,
    publishId: row.publishId,
    content: row.content,
    createdAt: row.createdAt,
    account: toMastodonAccount(urlToHandle(actor.id), actor),
    state: 'delivered',
  }

  const channels: string[] = []
  const batch: Record<string, ActorConversationProperties> = {}
  const actorConversations = await getActorConversationsBy(db, { topicId })
  for (const { id, actorId, properties } of actorConversations) {
    const unread = actor.id.toString() !== actorId
    if (properties.unread !== unread) {
      batch[id] = { ...properties, unread }
    }
    channels.push(`timeline:direct:${urlToHandle(actorId)}`)
  }
  await updateActorConversations(db, batch)
  await updateConversationTopic(db, topic, {
    lastObjectId: message.id.toString(),
  })

  for (const channel of channels) {
    await wsPublish(wsService, channel, { type: 'create', conversationId: topicId, status: message })
  }

  return message
}

export async function updateMessageInConversation(db: D1Database, wsService: Service, bz: Backblaze, r2: R2Bucket, topicId: string, actor: Actor, id: string, content: string) {
  const [item] = await getConversationMessagesBy(db, { id, actorId: actor.id.toString() })
  if (!item) {
    return
  }

  const { html, mediaIds } = await sanitize(content)
  const message = await updateConversationMessage(db, item, { content: html })
  if (message === null) {
    return
  }
  if (item.content) {
    const info = await sanitize(item.content)
    for (const mediaId of info.mediaIds) {
      if (mediaIds.includes(mediaId) === false) {
        bz.delete(mediaId)
        r2.delete(mediaId)
      }
    }
  }

  const actorConversations = await getActorConversationsBy(db, { topicId })
  for (const { actorId } of actorConversations) {
    await wsPublish(wsService, `timeline:direct:${urlToHandle(actorId)}`, { type: 'update', conversationId: topicId, status: message })
  }

  return message
}

export async function deleteMessageInConversation(db: D1Database, wsService: Service, bz: Backblaze, r2: R2Bucket, topicId: string, actor: Actor, id: string) {
  const topic = await getConversationTopic(db, topicId)
  if (!topic) {
    return
  }
  const [message] = await getConversationMessagesBy(db, { id, actorId: actor.id.toString() })
  if (!message) {
    return
  }

  if (topic.lastObjectId === message.id) {
    await updateConversationTopic(db, topic, { lastObjectId: undefined })
  }
  if (message.content) {
    const info = await sanitize(message.content)
    for (const mediaId of info.mediaIds) {
      bz.delete(mediaId)
      r2.delete(mediaId)
    }
  }
  await deleteConversationMessage(db, id)

  const actorConversations = await getActorConversationsBy(db, { topicId })
  for (const { actorId } of actorConversations) {
    await wsPublish(wsService, `timeline:direct:${urlToHandle(actorId)}`, { type: 'delete', conversationId: topicId, status: message })
  }

  return message
}
