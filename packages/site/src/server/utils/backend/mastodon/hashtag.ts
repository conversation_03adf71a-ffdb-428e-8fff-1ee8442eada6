import { PUBLIC_GROUP } from '../activitypub/activities'
import type { Actor } from '../activitypub/actors'
import { NOTE } from '../activitypub/objects/note'
import type { mastodon } from '../types'

export type Hashtag = string

const HASHTAG_RE = /#(\S+)/g

export interface TagRow {
  id: string
  display_name: string
  statuses_count?: number
  profiles_count?: number
  actors_count?: number
}

export function parseHashtags(input: string): Array<Hashtag> {
  const matches = input.matchAll(HASHTAG_RE)
  if (matches === null) {
    return []
  }
  return [...matches].map(match => match[1])
}

export async function insertHashtags(db: D1Database, domain: string, values: string[]): Promise<mastodon.v1.Tag[]> {
  const hashtags = values
    .map(value => [normalize(value), value])
    .filter(([value]) => Boolean(value))
    .filter((value, index, array) => array.indexOf(value) === index)

  const stmt = db.prepare('INSERT OR IGNORE INTO tags (id, display_name) VALUES (?, ?)')
  const batch: D1PreparedStatement[] = []
  for (const [name, displayName] of hashtags) {
    batch.push(stmt.bind(name, displayName))
  }
  await db.batch(batch)

  return getTags(db, domain, { ids: hashtags.map(([name]) => name), history: true })
}

export async function getTag(db: D1Database, domain: string, query: QueryTags): Promise<mastodon.v1.Tag | null> {
  const tags = await getTags(db, domain, query)
  if (tags.length === 0) {
    return null
  }
  return tags[0]
}

interface QueryTags {
  id?: string | URL
  ids?: Array<string | URL>
  objectId?: string | URL
  profileId?: string | URL
  actorId?: string | URL
  history?: boolean
}

export async function getTags(db: D1Database, domain: string, { id, ids, objectId, profileId, actorId, history = false }: QueryTags) {
  const rules = []
  const binds = []
  if (ids) {
    rules.push('id IN (SELECT value FROM json_each(?))')
    binds.push(JSON.stringify(ids.map(id => typeof id === 'string' ? normalize(id) : id.toString())))
  }
  if (id) {
    rules.push('id = ?')
    binds.push(typeof id === 'string' ? normalize(id) : id.toString())
  }
  if (objectId) {
    rules.push('id IN (SELECT tag_id FROM note_tags WHERE object_id = ?)')
    binds.push(typeof objectId === 'string' ? objectId : objectId.toString())
  }
  if (profileId) {
    rules.push('id IN (SELECT DISTINCT value FROM actors, json_each(actors.properties, "$.tags") WHERE actors.id = ?)')
    binds.push(typeof profileId === 'string' ? profileId : profileId.toString())
  }
  if (actorId) {
    rules.push('id IN (SELECT tag_id FROM actor_following_tags WHERE actor_id = ?)')
    binds.push(typeof actorId === 'string' ? actorId : actorId.toString())
  }

  let stats = ', 0 AS statuses_count, 0 AS actors_count'
  if (history) {
    const statuses = `
  SELECT count(*)
  FROM outbox_objects
  INNER JOIN objects ON objects.id = outbox_objects.object_id
  INNER JOIN note_tags ON objects.id = note_tags.object_id
  WHERE note_tags.tag_id = tags.id
  AND outbox_objects.target = '${PUBLIC_GROUP}'
  AND objects.type = '${NOTE}'
  AND json_extract(objects.properties, '$.inReplyTo') IS NULL
  `
    const actors = `
  SELECT count(*)
  FROM actor_following_tags
  WHERE actor_following_tags.tag_id = tags.id
  `
    const profiles = `
  SELECT count(*)
  FROM actors
  WHERE EXISTS (SELECT 1 FROM json_each(properties, '$.tags') WHERE value = tags.id)
  `
    stats = `, (${statuses}) AS statuses_count, (${profiles}) AS profiles_count, (${actors}) AS actors_count`
  }

  const where = rules.length ? `WHERE ${rules.join(' AND ')}` : ''
  const sql = `SELECT * ${stats} FROM tags ${where}`
  const { results, success, error } = await db.prepare(sql).bind(...binds).all<TagRow>()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  return results.map(result => toTag(result, domain))
}

export function toTag({ id, display_name, statuses_count, profiles_count, actors_count }: TagRow, domain: string): mastodon.v1.Tag {
  return {
    id,
    name: display_name,
    url: new URL(`/tags/${id}`, `https://${domain}`).href,
    history: [{
      day: `${Date.now()}`,
      uses: `${statuses_count}`,
      uses_profiles: `${profiles_count}`,
      accounts: `${actors_count}`,
    }],
  }
}

export async function createNoteTags(db: D1Database, objectId: string, tags: mastodon.v1.Tag[]) {
  const batch: D1PreparedStatement[] = [
    db.prepare('DELETE FROM note_tags WHERE object_id=?').bind(objectId),
  ]
  const stmt = db.prepare('INSERT OR IGNORE INTO note_tags (object_id, tag_id) VALUES (?, ?)')
  for (const { id } of tags) {
    batch.push(stmt.bind(objectId, id))
  }
  await db.batch(batch)
}

interface ActorTagRow {
  id: string
  actor_id: string
  tag_id: string
  published_date: string
  properties: string
}

export interface ActorTag {
  id: string
  actorId: string
  tagId: string
  publishedDate: string
  readDate?: string
}

function toActorTag({ id, actor_id, tag_id, published_date, properties }: ActorTagRow): ActorTag {
  return {
    id,
    actorId: actor_id,
    tagId: tag_id,
    publishedDate: published_date,
    readDate: JSON.parse(properties).readDate,
  }
}

export async function countActorTag(db: D1Database, actor: Actor, tag: mastodon.v1.Tag) {
  const query = 'SELECT count(*) as count FROM actor_following_tags WHERE actor_id=? AND tag_id=?'
  const out = await db.prepare(query).bind(actor.id.toString(), tag.id).first<{ count: number }>()
  return out?.count ?? 0
}

export async function createActorTag(db: D1Database, actor: Actor, tag: mastodon.v1.Tag) {
  const id = crypto.randomUUID()
  const query = 'INSERT OR IGNORE INTO actor_following_tags (id, actor_id, tag_id) VALUES (?, ?, ?) RETURNING *'
  const row = await db.prepare(query).bind(id, actor.id.toString(), tag.id).first<ActorTagRow>()
  return row ? toActorTag(row) : null
}

export async function deleteActorTag(db: D1Database, actor: Actor, tag: mastodon.v1.Tag) {
  const query = 'DELETE FROM actor_following_tags WHERE actor_id=? AND tag_id=? RETURNING *'
  const row = await db.prepare(query).bind(actor.id.toString(), tag.id).first<ActorTagRow>()
  return row ? toActorTag(row) : null
}

interface QueryActorTags {
  actorId?: string | URL
  tagId?: string | URL
  tagIds?: Array<string | URL>
}

type ActorTagData = Partial<ActorTag> & { properties?: string }

export async function updateActorTags(db: D1Database, { readDate, publishedDate, properties }: ActorTagData, { actorId, tagId, tagIds }: QueryActorTags) {
  const values = []
  const binds = []
  if (publishedDate === 'now') {
    values.push('published_date = STRFTIME(\'%Y-%m-%d %H:%M:%f\', \'NOW\')')
  }
  else if (publishedDate) {
    values.push('published_date=?')
    binds.push(publishedDate)
  }
  if (readDate === 'now') {
    values.push('properties=json_set(properties, \'$.readDate\', STRFTIME(\'%Y-%m-%d %H:%M:%f\', \'NOW\'))')
  }
  else if (readDate) {
    values.push('properties=json_set(properties, \'$.readDate\', ?)')
    binds.push(readDate)
  }
  if (properties) {
    values.push('properties=?')
    binds.push(JSON.stringify(properties))
  }
  if (Object.keys(values).length) {
    const rules = []
    if (tagIds) {
      rules.push('tag_id IN (SELECT value FROM json_each(?))')
      binds.push(JSON.stringify(tagIds.map(id => typeof id === 'string' ? id : id.toString())))
    }
    if (tagId) {
      rules.push('tag_id = ?')
      binds.push(typeof tagId === 'string' ? tagId : tagId.toString())
    }
    if (actorId) {
      rules.push('actor_id = ?')
      binds.push(typeof actorId === 'string' ? actorId : actorId.toString())
    }
    const where = rules.length ? `WHERE ${rules.join(' AND ')}` : ''
    const sql = `UPDATE actor_following_tags SET ${values.join(',')} ${where} RETURNING *`
    const row = await db.prepare(sql).bind(...binds).first<ActorTagRow>()
    return row ? toActorTag(row) : null
  }
}

export async function getActorTags(db: D1Database, { actorId, tagId, tagIds }: QueryActorTags) {
  const binds = []
  const rules = []
  if (tagIds) {
    rules.push('tag_id IN (SELECT value FROM json_each(?))')
    binds.push(JSON.stringify(tagIds.map(id => typeof id === 'string' ? id : id.toString())))
  }
  if (tagId) {
    rules.push('tag_id = ?')
    binds.push(typeof tagId === 'string' ? tagId : tagId.toString())
  }
  if (actorId) {
    rules.push('actor_id = ?')
    binds.push(typeof actorId === 'string' ? actorId : actorId.toString())
  }
  const where = rules.length ? `WHERE ${rules.join(' AND ')}` : ''
  const sql = `SELECT * FROM actor_following_tags ${where}`
  const { results, success, error } = await db.prepare(sql).bind(...binds).all<ActorTagRow>()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  return results.map(toActorTag)
}

export function normalize(str: string) {
  return str.replace(/[^[:alnum]\u0E47-\u0E4E_\u00B7\u30FB\u200c\]/g, '').toLowerCase()
}
