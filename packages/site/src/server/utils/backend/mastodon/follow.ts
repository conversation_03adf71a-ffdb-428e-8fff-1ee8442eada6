import { getResultsField } from './utils'
import type { Actor } from '~/server/utils/backend/activitypub/actors'
import * as actors from '~/server/utils/backend/activitypub/actors'
import { urlToHandle } from '~/server/utils/backend/utils/handle'

const STATE_PENDING = 'pending'
export const STATE_ACCEPTED = 'accepted'

// During a migration we move the followers from the old Actor to the new
export async function moveFollowers(db: D1Database, actor: Actor, followers: Array<string>): Promise<void> {
  const actorId = actor.id.toString()
  const actorAcc = urlToHandle(actor.id)

  const batch = []
  const stmt = db.prepare('INSERT OR IGNORE INTO actor_following_actors (id, actor_id, target_actor_id, target_actor_acct, state) VALUES (?, ?, ?, ?, ?)')
  for (let i = 0; i < followers.length; i++) {
    const follower = new URL(followers[i])
    const followActor = await actors.getAndCache(follower, db)
    batch.push(stmt.bind(crypto.randomUUID(), followActor.id.toString(), actorId, actorAcc, STATE_ACCEPTED))
  }
  await db.batch(batch)
}

export async function moveFollowing(db: D1Database, actor: Actor, followingActors: Array<string>): Promise<void> {
  const actorId = actor.id.toString()

  const batch = []
  const stmt = db.prepare('INSERT OR IGNORE INTO actor_following_actors (id, actor_id, target_actor_id, target_actor_acct, state) VALUES (?, ?, ?, ?, ?)')
  for (let i = 0; i < followingActors.length; i++) {
    const following = new URL(followingActors[i])
    const followingActor = await actors.getAndCache(following, db)
    const actorAcc = urlToHandle(followingActor.id)
    batch.push(stmt.bind(crypto.randomUUID(), actorId, followingActor.id.toString(), actorAcc, STATE_ACCEPTED))
  }
  await db.batch(batch)
}

interface ActorFollowingRow {
  id: string
  actor_id: string
  target_actor_id: string
  published_date: string
  properties: string
}

export interface ActorFollowing {
  id: string
  actorId: string
  targetActorId: string
  publishedDate: string
  readDate?: string
}

function toActorFollowing({ id, actor_id, target_actor_id, published_date, properties }: ActorFollowingRow): ActorFollowing {
  return {
    id,
    actorId: actor_id,
    targetActorId: target_actor_id,
    publishedDate: published_date,
    readDate: JSON.parse(properties).readDate,
  }
}

// Add a pending following
export async function addFollowing(db: D1Database, actor: Actor, target: Actor, targetAcct: string) {
  const query = 'INSERT OR IGNORE INTO actor_following_actors (id, actor_id, target_actor_id, state, target_actor_acct) VALUES (?, ?, ?, ?, ?) RETURNING *'
  const row = await db.prepare(query).bind(crypto.randomUUID(), actor.id.toString(), target.id.toString(), STATE_PENDING, targetAcct).first<ActorFollowingRow>()
  return row ? toActorFollowing(row) : null
}

// Accept the pending following request
export async function acceptFollowing(db: D1Database, actor: Actor, target: Actor) {
  const query = 'UPDATE actor_following_actors SET state=? WHERE actor_id=? AND target_actor_id=? AND state=? RETURNING *'
  const row = await db.prepare(query).bind(STATE_ACCEPTED, actor.id.toString(), target.id.toString(), STATE_PENDING).first<ActorFollowingRow>()
  return row ? toActorFollowing(row) : null
}

export async function removeFollowing(db: D1Database, actor: Actor, target: Actor) {
  const query = 'DELETE FROM actor_following_actors WHERE actor_id=? AND target_actor_id=? RETURNING *'
  const row = await db.prepare(query).bind(actor.id.toString(), target.id.toString()).first<ActorFollowingRow>()
  return row ? toActorFollowing(row) : null
}

export function getFollowingAcct(db: D1Database, actor: Actor): Promise<Array<string>> {
  const query = 'SELECT target_actor_acct FROM actor_following_actors WHERE actor_id=? AND state=?'
  const statement = db.prepare(query).bind(actor.id.toString(), STATE_ACCEPTED)
  return getResultsField(statement, 'target_actor_acct')
}

export function getFollowingRequestedAcct(db: D1Database, actor: Actor): Promise<Array<string>> {
  const query = 'SELECT target_actor_acct FROM actor_following_actors WHERE actor_id=? AND state=?'
  const statement = db.prepare(query).bind(actor.id.toString(), STATE_PENDING)
  return getResultsField(statement, 'target_actor_acct')
}

export function getFollowingId(db: D1Database, actor: Actor): Promise<Array<string>> {
  const query = 'SELECT target_actor_id FROM actor_following_actors WHERE actor_id=? AND state=?'
  const statement = db.prepare(query).bind(actor.id.toString(), STATE_ACCEPTED)
  return getResultsField(statement, 'target_actor_id')
}

export function getFollowers(db: D1Database, actor: Actor): Promise<Array<string>> {
  const query = 'SELECT actor_id FROM actor_following_actors WHERE target_actor_id=? AND state=?'
  const statement = db.prepare(query).bind(actor.id.toString(), STATE_ACCEPTED)
  return getResultsField(statement, 'actor_id')
}

interface QueryActorFollowing {
  actorId?: string | URL
  targetActorId?: string | URL
  targetActorIds?: Array<string | URL>
  state?: string
}

export async function getActorFollowings(db: D1Database, query: QueryActorFollowing) {
  const binds: string[] = []
  const rules = toQuery(query, binds)
  const where = rules.length ? `WHERE ${rules.join(' AND ')}` : ''
  const sql = `SELECT * FROM actor_following_actors ${where}`
  const { results, success, error } = await db.prepare(sql).bind(...binds).all<ActorFollowingRow>()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  return results.map(toActorFollowing)
}

type ActorFollowerData = Partial<ActorFollowing> & { properties?: string }

export async function updateActorFollowing(db: D1Database, { readDate, publishedDate, properties }: ActorFollowerData, query: QueryActorFollowing) {
  const values = []
  const binds = []
  if (publishedDate === 'now') {
    values.push('published_date = STRFTIME(\'%Y-%m-%d %H:%M:%f\', \'NOW\')')
  }
  else if (publishedDate) {
    values.push('published_date=?')
    binds.push(publishedDate)
  }
  if (readDate === 'now') {
    values.push('properties=json_set(properties, \'$.readDate\', STRFTIME(\'%Y-%m-%d %H:%M:%f\', \'NOW\'))')
  }
  else if (readDate) {
    values.push('properties=json_set(properties, \'$.readDate\', ?)')
    binds.push(readDate)
  }
  if (properties) {
    values.push('properties=?')
    binds.push(JSON.stringify(properties))
  }
  if (Object.keys(values).length) {
    const rules = toQuery(query, binds)
    const where = rules.length ? `WHERE ${rules.join(' AND ')}` : ''
    const sql = `UPDATE actor_following_actors SET ${values.join(',')} ${where} RETURNING *`
    const row = await db.prepare(sql).bind(...binds).first<ActorFollowingRow>()
    return row ? toActorFollowing(row) : null
  }
}

function toQuery({ actorId, targetActorId, targetActorIds, state }: QueryActorFollowing, binds: string[]) {
  const rules: string[] = []
  if (targetActorIds) {
    rules.push('target_actor_id IN (SELECT value FROM json_each(?))')
    binds.push(JSON.stringify(targetActorIds.map(id => typeof id === 'string' ? id : id.toString())))
  }
  if (targetActorId) {
    rules.push('target_actor_id = ?')
    binds.push(typeof targetActorId === 'string' ? targetActorId : targetActorId.toString())
  }
  if (actorId) {
    rules.push('actor_id = ?')
    binds.push(typeof actorId === 'string' ? actorId : actorId.toString())
  }
  if (state) {
    rules.push('state = ?')
    binds.push(state)
  }
  return rules
}
