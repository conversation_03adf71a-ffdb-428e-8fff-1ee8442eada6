import { urlT<PERSON><PERSON>andle } from '../utils/handle'
import { toMastodonAccount } from './account'
import { toMastodonStatus, toMastodonStatusFromRow, type MastodonStatusStats } from './status'
import type { mastodon } from '#shared/types'
import type { ActorInnerRow, Actor } from '~/server/utils/backend/activitypub/actors'
import { actorFromInnerRow } from '~/server/utils/backend/activitypub/actors'
import type { APObject, APObjectRow } from '~/server/utils/backend/activitypub/objects'

export async function insertReply(db: D1Database, actor: Actor, obj: APObject, inReplyToObj: APObject) {
  const id = crypto.randomUUID()
  const query = `
        INSERT INTO actor_replies (id, actor_id, object_id, in_reply_to_object_id)
        VALUES (?, ?, ?, ?)
    `
  const { success, error } = await db
    .prepare(query)
    .bind(id, actor.id.toString(), obj.id.toString(), inReplyToObj.id.toString())
    .run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

export async function getReplies(domain: string, db: D1Database, obj: APObject): Promise<Array<mastodon.v1.Status>> {
  const QUERY = `
SELECT objects.*,
       actors.id as actor_id,
       actors.cdate as actor_cdate,
       actors.properties as actor_properties,
       actor_replies.actor_id as publisher_actor_id,
       (SELECT count(*) FROM actor_favourites WHERE actor_favourites.object_id=objects.id) as favourites_count,
       (SELECT count(*) FROM actor_reblogs WHERE actor_reblogs.object_id=objects.id) as reblogs_count,
       (SELECT count(*) FROM actor_replies WHERE actor_replies.in_reply_to_object_id=objects.id) as replies_count
FROM actor_replies
INNER JOIN objects ON objects.id=actor_replies.object_id
INNER JOIN actors ON actors.id=actor_replies.actor_id
WHERE actor_replies.in_reply_to_object_id=?
ORDER by actor_replies.cdate DESC
LIMIT ?
`
  const DEFAULT_LIMIT = 20

  const { success, error, results } = await db.prepare(QUERY).bind(obj.id.toString(), DEFAULT_LIMIT).all()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }

  const out: Array<mastodon.v1.Status> = []

  for (let i = 0, len = results.length; i < len; i++) {
    const status = await toMastodonStatusFromRow(domain, db, results[i])
    if (status !== null) {
      out.push(status)
    }
  }

  return out
}

export async function getDescendants(domain: string, db: D1Database, obj: APObject, actor?: Actor | null): Promise<Array<mastodon.v1.Status>> {
  const binds = [obj.id.toString()]
  let actorInfo = `
    0 as reblogged,
    0 as favourited,
    `
  if (actor) {
    binds.push(actor.id.toString())
    actorInfo = `
    (SELECT count(*) > 0 FROM actor_reblogs WHERE actor_reblogs.object_id=objects.id AND actor_reblogs.actor_id=?2) as reblogged,
    (SELECT count(*) > 0 FROM actor_favourites WHERE actor_favourites.object_id=objects.id AND actor_favourites.actor_id=?2) as favourited,
    `
  }
  const query = `
  WITH RECURSIVE
  search_tree(id, level) AS (
    VALUES(?1, 0)
    UNION ALL
    SELECT objects.id, search_tree.level + 1
      FROM objects JOIN search_tree ON objects.reply_to_object_id = search_tree.id
     ORDER BY 1 DESC
  )
SELECT search_tree.level,
       objects.*,
       actors.id as actor_id, actors.cdate as actor_cdate, actors.properties as actor_properties,
       ${actorInfo}
       (SELECT count(*) FROM actor_favourites WHERE actor_favourites.object_id=objects.id) as favourites_count,
       (SELECT count(*) FROM actor_reblogs WHERE actor_reblogs.object_id=objects.id) as reblogs_count,
       (SELECT count(*) FROM actor_replies WHERE actor_replies.in_reply_to_object_id=objects.id) as replies_count
FROM search_tree
INNER JOIN objects ON objects.id = search_tree.id
INNER JOIN actors ON actors.id = objects.original_actor_id
WHERE search_tree.level > 0
`
  console.debug(query)
  console.debug(binds)
  const { success, error, results } = await db.prepare(query).bind(...binds).all<APObjectRow & MastodonStatusStats & ActorInnerRow>()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }

  const out: Array<mastodon.v1.Status> = []
  for (const row of results) {
    const properties = JSON.parse(row.properties)
    const author = actorFromInnerRow(row)
    const acct = urlToHandle(author.id)
    const account = toMastodonAccount(acct, author)
    const status = toMastodonStatus(domain, account, row, author, properties)
    out.push(status)
  }
  return out
}
