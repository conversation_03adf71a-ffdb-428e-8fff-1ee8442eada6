export interface Cache {
  fetch<T>(key: string, fn: () => Promise<T | undefined>): Promise<T | undefined>
  get<T>(key: string): Promise<T | undefined>
  put<T>(key: string, value: T): Promise<void>
}

export const emptyCache: Cache = {
  fetch: function <T>(key: string, fn: () => Promise<T | undefined>): Promise<T | undefined> {
    return fn()
  },
  get: function <T>(key: string): Promise<T | undefined> {
    throw new Error('Function not implemented.')
  },
  put: function <T>(key: string, value: T): Promise<void> {
    throw new Error('Function not implemented.')
  },
}

export function cacheFromEnv(DO_CACHE: DurableObjectNamespace, waitUntil?: (fn: Promise<any>) => void): Cache {
  const id = DO_CACHE.idFromName('cachev1')
  const stub = DO_CACHE.get(id)

  return {
    async fetch<T>(key: string, fn: () => Promise<T | undefined>): Promise<T | undefined> {
      let value = await this.get<T>(key)
      if (!value) {
        value = await fn()
        if (value) {
          if (waitUntil) {
            waitUntil(this.put(key, value))
          }
          else {
            await this.put(key, value)
          }
        }
      }
      return value
    },

    async get<T>(key: string): Promise<T | undefined> {
      const res = await stub.fetch('http://cache/' + key)
      if (!res.ok) {
        if (res.status === 404) {
          return
        }
        throw new Error(`DO cache returned ${res.status}: ${await res.text()}`)
      }
      return (await res.json()) as T
    },

    async put<T>(key: string, value: T): Promise<void> {
      const res = await stub.fetch('http://cache/', {
        method: 'PUT',
        body: JSON.stringify({ key, value }),
      })
      if (!res.ok) {
        throw new Error(`DO cache returned ${res.status}: ${await res.text()}`)
      }
    },
  }
}
