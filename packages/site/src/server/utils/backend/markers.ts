import { qb } from './database/querybuilder'

export interface Marker {
  id: string
  timeline: string
  lastReadId: string
  published: string
}

export async function getMarkers(db: D1Database, actorId: string, timelines: string[] = []) {
  const query = `SELECT * FROM markers WHERE actor_id = ?1 AND mastodon_id IN ${qb.set('?2')}`
  const { results } = await db.prepare(query).bind(actorId, JSON.stringify(timelines)).all()
  return results.map(markerFromRow)
}

export async function createMarker(db: D1Database, actorId: string, timeline: string, lastReadId: string) {
  const query = 'INSERT INTO markers(actor_id, timeline) VALUES(?, ?, ?) RETURNING *'
  const row = await db.prepare(query).bind(actorId, timeline, lastReadId).first()
  return markerFromRow(row)
}

function markerFromRow(row: any): Marker {
  return {
    id: row.rowid,
    timeline: row.timeline,
    lastReadId: row.last_read_id,
    published: new Date(row.cdate).toISOString(),
  }
}
