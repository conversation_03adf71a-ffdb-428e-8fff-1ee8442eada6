export async function wsPublish(wsService: Service, channel: string, payload: Record<string, any>) {
  console.log(`publish ${payload.type.toUpperCase()} to ${channel}`)
  const res = await wsService.fetch(new Request('https://wspool.internal/wspool/publish', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    body: JSON.stringify({ channel, payload }),
  }))
  if (!res.ok) {
    throw new Error(`SWPOOL returned ${res.status}: ${await res.text()}`)
  }
}
