import { IMAGE, mastodonIdSymbol, type APObject, type Document, type Image } from '~/server/utils/backend/activitypub/objects'
import type { mastodon } from '#shared/types'

export function fromObject(obj: APObject): mastodon.v1.MediaAttachment {
  if (obj.type === IMAGE) {
    return fromObjectImage(obj as Image)
  }
  else if (obj.type === 'Video') {
    return fromObjectVideo(obj)
  }
  else if (obj.type === 'Audio') {
    return fromObjectAudio(obj)
  }
  else if (obj.type === 'Document') {
    return fromObjectDocument(obj as Document)
  }
  else {
    throw new Error(`unsupported media type ${obj.type}: ${JSON.stringify(obj)}`)
  }
}

const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
function fromObjectDocument(obj: Document): mastodon.v1.MediaAttachment {
  if (imageTypes.includes(obj.mediaType as string)) {
    return fromObjectImage(obj)
  }
  else if (obj.mediaType.startsWith('video/')) {
    return fromObjectVideo(obj)
  }
  else {
    throw new Error(`unsupported media Document type: ${JSON.stringify(obj)}`)
  }
}

function fromObjectImage(obj: Image): mastodon.v1.MediaAttachment {
  return {
    id: obj[mastodonIdSymbol] || obj.url.href,
    type: 'image',
    url: new URL(obj.url).href,
    previewUrl: new URL(obj.url).href,
    meta: {},
    description: obj.description || '',
    blurhash: '',
  }
}

function fromObjectVideo(obj: APObject & { description?: string }): mastodon.v1.MediaAttachment {
  return {
    id: obj.url.toString(),
    type: 'video',
    url: new URL(obj.url).href,
    previewUrl: new URL(obj.url).href,
    meta: {},
    description: obj.description || '',
    blurhash: '',
  }
}

function fromObjectAudio(obj: APObject & { description?: string }): mastodon.v1.MediaAttachment {
  return {
    id: obj.url.toString(),
    type: 'audio',
    url: new URL(obj.url).href,
    previewUrl: new URL(obj.url).href,
    meta: {},
    description: obj.description || '',
    blurhash: '',
  }
}
