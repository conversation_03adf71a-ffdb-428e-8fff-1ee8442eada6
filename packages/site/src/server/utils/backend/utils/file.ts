const mimeExtension = new Map([
  ['image/jpeg', 'jpg'],
  ['image/png', 'png'],
  ['image/gif', 'gif'],
  ['image/webp', 'webp'],
  ['image/avif', 'avif'],
  ['video/webm', 'webm'],
  ['video/mp4', 'mp4'],
  ['video/ogg', 'ogv'],
  ['audio/mp3', 'mp3'],
  ['audio/mpeg', 'mpga'],
  ['audio/webm', 'weba'],
  ['audio/wave', 'wav'],
])

interface ObjectType {
  type: string
  fileExtension: string
}

export function getObjectType(file: File): ObjectType | undefined {
  let mime: [string, string] | undefined
  let ext = mimeExtension.get(file.type)
  if (ext) {
    mime = [file.type, ext]
  }
  if (!mime) {
    const extensions = [...mimeExtension.values(), 'jpeg']
    ext = file.name.split('.').pop()
    ext = ext === 'jpeg' ? 'jpg' : ext
    if (ext && extensions.includes(ext)) {
      mime = Array.from(mimeExtension.entries()).find(([, value]) => ext === value)
    }
  }
  if (mime) {
    const [mimeType, fileExtension] = mime
    let type: 'Image' | 'Video' | 'Audio' | undefined
    if (mimeType.startsWith('image/')) {
      type = 'Image'
    }
    else if (mimeType.startsWith('video/')) {
      type = 'Video'
    }
    else if (mimeType.startsWith('audio/')) {
      type = 'Audio'
    }
    if (type) {
      return { fileExtension, type }
    }
  }
}
