import { describe, it, expect } from 'vitest'
import { htmlToText } from './content-parse'

describe('htmlToText', () => {
  it('should convert basic HTML to text', () => {
    const html = '<p>Hello world</p>'
    expect(htmlToText(html)).toBe('\nHello world\n')
  })

  it('should handle links with text', () => {
    const html = '<p>Check out <a href="https://example.com">this link</a></p>'
    expect(htmlToText(html)).toBe('\nCheck out [this link](https://example.com)\n')
  })

  it('should handle links without text', () => {
    const html = '<p><a href="https://example.com"></a></p>'
    expect(htmlToText(html)).toBe('\n[https://example.com](https://example.com)\n')
  })

  it('should handle multiple links', () => {
    const html = '<p><a href="https://example1.com">Link 1</a> and <a href="https://example2.com"></a></p>'
    expect(htmlToText(html)).toBe('\n[Link 1](https://example1.com) and [https://example2.com](https://example2.com)\n')
  })

  it('should handle nested elements', () => {
    const html = '<div><p>Text with <strong>bold</strong> and <em>italic</em></p></div>'
    expect(htmlToText(html)).toBe('\n\nText with **bold** and *italic*\n\n')
  })

  it('should handle images', () => {
    const html = '<p>An image: <img src="image.jpg" alt="Alt text"></p>'
    expect(htmlToText(html)).toBe('\nAn image: ![Alt text](image.jpg)\n')
  })

  it('should handle code blocks', () => {
    const html = '<pre><code class="language-js">const x = 1;</code></pre>'
    expect(htmlToText(html)).toBe('\n```js\nconst x = 1;\n```\n')
  })

  it('should handle empty content', () => {
    expect(htmlToText('')).toBe('')
  })

  it('should handle malformed HTML', () => {
    // The function should not throw an error for malformed HTML
    expect(() => htmlToText('<p>Unclosed tag')).not.toThrow()
  })

  it('should handle links in the isEmpty function context', () => {
    // This test simulates how the isEmpty function processes content
    const html = '<a href="https://example.com"></a>'
    const text = htmlToText(html)
      .trim()
      .replace(/^(@\S+\s?)+/, '')
      .replaceAll(/```/g, '')
      .trim()

    // The text should not be empty, which would make isEmpty return false
    expect(text.length).toBeGreaterThan(0)
  })
})
