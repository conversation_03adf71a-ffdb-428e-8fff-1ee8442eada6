import type { Node } from 'ultrahtml'
import { TEXT_NODE } from 'ultrahtml'

// A tree transform function takes an ultrahtml Node object and returns new content that
// will replace the given node in the tree.
// Returning a null removes the node from the tree. Strings get converted to text nodes.
// The input node's children have been transformed before the node itself gets transformed.
export type Transform = (node: Node, root: Node) => (Node | string)[] | Node | string | null

// Helpers for transforming (filtering, modifying, ...) a parsed HTML tree
// by running the given chain of transform functions one-by-one.
export function transformSync(doc: Node, transforms: Transform[]) {
  function visit(node: Node, transform: Transform, root: Node) {
    if (Array.isArray(node.children)) {
      const children = [] as (Node | string)[]
      for (let i = 0; i < node.children.length; i++) {
        const result = visit(node.children[i], transform, root)
        if (Array.isArray(result)) {
          children.push(...result)
        }
        else if (result) {
          children.push(result)
        }
      }
      node.children = children.map((value) => {
        if (typeof value === 'string') {
          return { type: TEXT_NODE, value, parent: node }
        }
        value.parent = node
        return value
      })
    }
    return transform(node, root)
  }

  for (const transform of transforms) {
    doc = visit(doc, transform, doc) as Node
  }

  return doc
}
