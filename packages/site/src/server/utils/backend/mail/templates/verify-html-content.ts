import type { BodyPayload } from './verify'

export function verifyEmailHtml(greeting: string, body: string, p: BodyPayload) {
  const bodyHtml = `<p>${body.replace(/\n/, '').replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title></title>
  <style>
  .logo { 
    display: block;
    height: 2rem;
    width: 9.6373626374rem;
    background-repeat: no-repeat;
    background-size: 100%;
    background-image: url(https://individuallist.xyz/logo-individuallist-v2.png)
  }
  </style>
</head>
<body>
  <table>
    <tr>
      <td>
      <span class="logo"></span>
      </td>
    </tr>
    <tr>
      <td>
        ${greeting}
      </td>
    </tr>
    <tr>
      <td>
        <h1>${p.ticket}</h1>
      </td>
    </tr>
    <tr>
      <td>
        ${bodyHtml}
      </td>
    </tr>
  </table>
</body>
</html>
`
}
