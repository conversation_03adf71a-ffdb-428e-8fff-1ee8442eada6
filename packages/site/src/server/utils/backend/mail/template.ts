export const appName = 'individuallist.xyz'
export const senderAddress = '"individuallist.xyz" <<EMAIL>>'

interface MailPayload {
  to: string
  from?: string
  subject: string
  text?: string
  html?: string
}

export abstract class TemplateMailRequest extends Request {
  protected constructor(url: URL, body: MailPayload) {
    super(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        'Accept': 'application/json, text/plain, */*',
      },
      body: JSON.stringify(body),
    })
  }
}
