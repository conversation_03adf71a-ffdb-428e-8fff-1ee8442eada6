import { qb } from '../../database/querybuilder'
import { actor<PERSON><PERSON><PERSON>nnerR<PERSON>, type Actor, type ActorInnerRow } from '~/server/utils/backend/activitypub/actors'

interface ActorConversationRow {
  id: string
  actor_id: string
  conversation_id: string
  properties: string
}

export interface ActorConversationProperties {
  unread?: boolean
}

interface ActorConversationData {
  id: string
  actorId: string
  actor: Actor
  conversationId: string
  properties: ActorConversationProperties
}

function toActorConversation({ id, conversation_id, properties }: ActorConversationRow, actor: Actor): ActorConversationData {
  return {
    id,
    conversationId: conversation_id,
    actorId: actor.id.toString(),
    properties: JSON.parse(properties),
    actor,
  }
}

export async function getActorConversationsBy(db: D1Database, { id, topicId, actorId, actorIds, participantId }: { id?: string, topicId?: string, actorId?: string, actorIds?: string[], participantId?: string }) {
  const rules = []
  const binds = []
  if (id) {
    rules.push('id = ?')
    binds.push(id)
  }
  if (topicId) {
    rules.push('conversation_id = ?')
    binds.push(topicId)
  }
  if (actorId || actorIds) {
    rules.push(`actor_id IN ${qb.set('?')}`)
    binds.push(JSON.stringify([
      ...(actorId ? [actorId] : []),
      ...(actorIds ? actorIds : []),
    ]))
  }
  if (participantId) {
    rules.push('conversation_id IN (SELECT conversation_id FROM actor_conversations WHERE actor_id = ?)')
    binds.push(participantId)
  }
  const where = rules.length ? `WHERE ${rules.join(' AND ')}` : ''
  const query = `
  SELECT actor_conversations.*,
         actors.id as actor_id,
         actors.cdate as actor_cdate,
         actors.properties as actor_properties
  FROM actor_conversations
  INNER JOIN actors ON actors.id = actor_conversations.actor_id
  ${where}
 `
  console.debug(query, binds)
  const { results, success, error } = await db.prepare(query).bind(...binds).all<ActorConversationRow & ActorInnerRow>()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  return results.map(row => toActorConversation(row, actorFromInnerRow(row)))
}

export async function createActorConversations(db: D1Database, conversationId: string, actorIds: string[]) {
  const stmt = db.prepare('INSERT INTO actor_conversations(id, conversation_id, actor_id) VALUES(?, ?, ?)')
  const rows = await db.batch(actorIds.map(actorId => stmt.bind(crypto.randomUUID(), conversationId, actorId)))
  for (const row of rows) {
    if (!row.success) {
      throw new Error('SQL error: ' + row.error)
    }
  }
}

export async function updateActorConversations(db: D1Database, data: Record<string, ActorConversationProperties>) {
  if (Object.keys(data).length) {
    const stmt = db.prepare('UPDATE actor_conversations SET properties=? WHERE id=?')
    const rows = await db.batch(Object.entries(data).map(([id, properties]) => stmt.bind(JSON.stringify(properties), id)))
    for (const row of rows) {
      if (!row.success) {
        throw new Error('SQL error: ' + row.error)
      }
    }
  }
}

export async function deleteActorConversations(db: D1Database, id: string) {
  const query = 'DELETE FROM actor_conversations WHERE id=?'
  const { success, error } = await db.prepare(query).bind(id).run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}
