import merge from 'lodash.merge'

interface ConversationRow {
  id: string
  properties: string
}

interface ConversationProperties {
  lastObjectId?: string
}

export interface ConversationTopic {
  id: string
  lastObjectId?: string
}

function toConversation(row: ConversationRow): ConversationTopic {
  const properties: ConversationProperties = JSON.parse(row.properties)
  return {
    id: row.id,
    lastObjectId: properties.lastObjectId,
  }
}

function toProperties({ lastObjectId }: ConversationTopic): ConversationProperties {
  return {
    lastObjectId,
  }
}

export async function getConversationTopic(db: D1Database, id: string) {
  const query = 'SELECT * FROM conversations WHERE id=?'
  const row = await db.prepare(query).bind(id).first<ConversationRow>()
  if (!row) {
    return null
  }
  return toConversation(row)
}

export async function createConversationTopic(db: D1Database) {
  const query = 'INSERT INTO conversations(id) VALUES(?) RETURNING *'
  const row = await db.prepare(query).bind(crypto.randomUUID()).first<ConversationRow>()
  if (!row) {
    return null
  }
  return toConversation(row)
}

export async function updateConversationTopic(db: D1Database, item: ConversationTopic, data: ConversationProperties) {
  const query = 'UPDATE conversations SET properties=? WHERE id=?'
  const { success, error } = await db.prepare(query).bind(JSON.stringify(merge(toProperties(item), data)), item.id).run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

export async function deleteConversationTopic(db: D1Database, id: string) {
  const query = 'DELETE FROM conversations WHERE id=?'
  const { success, error } = await db.prepare(query).bind(id).run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}
