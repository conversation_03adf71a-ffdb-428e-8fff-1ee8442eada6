import merge from 'lodash.merge'
import { qb } from '../../database/querybuilder'
import { type Actor, actorFromInnerRow, type ActorInnerRow } from '~/server/utils/backend/activitypub/actors'
import type { ConversationMessageData } from '#shared/types/conversation'

interface ConversationMessageRow {
  id: string
  actor_id: string
  conversation_id: string
  properties: string
  cdate: string
}

interface ConversationMessageProperties {
  publish_id?: string
  content: string
}

function toConversationMessage(row: ConversationMessageRow, actor: Actor): ConversationMessageData {
  const properties: ConversationMessageProperties = JSON.parse(row.properties)
  return {
    id: row.id,
    conversationId: row.conversation_id,
    publishId: properties.publish_id,
    content: properties.content,
    createdAt: new Date(row.cdate).getTime(),
    actor,
  }
}

function toProperties({ publishId, content }: Partial<Pick<ConversationMessageData, 'publishId' | 'content'>>): Partial<ConversationMessageProperties> {
  return {
    publish_id: publishId,
    content,
  }
}

export async function getConversationMessagesBy(db: D1Database, { id, actorId, actorIds, topicId }: { id?: string, actorId?: string, actorIds?: string[], topicId?: string }) {
  const rules = []
  const binds = []
  if (id) {
    rules.push('actor_conversation_objects.id = ?')
    binds.push(id)
  }
  if (actorId || actorIds) {
    rules.push(`actor_id IN ${qb.set('?')}`)
    binds.push(JSON.stringify([
      ...(actorId ? [actorId] : []),
      ...(actorIds ? actorIds : []),
    ]))
  }
  if (topicId) {
    rules.push('actor_conversation_objects.conversation_id = ?')
    binds.push(topicId)
  }
  const where = rules.length ? `AND ${rules.join(' AND ')}` : ''
  const sql = `
SELECT actor_conversation_objects.*,
       actors.cdate as actor_cdate,
       actors.properties as actor_properties
FROM actor_conversation_objects
INNER JOIN actors ON actors.id = actor_conversation_objects.actor_id
WHERE 1=1 ${where}
ORDER by actor_conversation_objects.cdate DESC
`
  console.debug(sql, binds)
  const { results, success, error } = await db.prepare(sql).bind(...binds).all<ConversationMessageRow & ActorInnerRow>()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  return results.map(row => toConversationMessage(row, actorFromInnerRow(row)))
}

export async function createConversationMessage(db: D1Database, topicId: string, publishId: string, content: string, actor: Actor, id = crypto.randomUUID()) {
  const data: Omit<ConversationMessageRow, 'cdate'> = {
    id,
    actor_id: actor.id.toString(),
    conversation_id: topicId,
    properties: JSON.stringify(toProperties({ publishId, content })),
  }
  const sql = `INSERT INTO actor_conversation_objects(${Object.keys(data).join(', ')}) VALUES(${Array(Object.keys(data).length).fill('?').join(', ')}) RETURNING *`
  const binds = Object.values(data)
  console.debug(sql, binds)
  const row = await db.prepare(sql).bind(...binds).first<ConversationMessageRow>()
  if (!row) {
    return null
  }
  return toConversationMessage(row, actor)
}

export async function updateConversationMessage(db: D1Database, item: ConversationMessageData, data: Partial<Pick<ConversationMessageData, 'publishId' | 'content'>>) {
  const sql = 'UPDATE actor_conversation_objects SET properties=? WHERE id=? RETURNING *'
  const binds = [JSON.stringify(merge(toProperties(item), toProperties(data))), item.id]
  console.debug(sql, binds)
  const row = await db.prepare(sql).bind(...binds).first<ConversationMessageRow>()
  if (!row) {
    return null
  }
  return toConversationMessage(row, item.actor)
}

export async function deleteConversationMessage(db: D1Database, id: string) {
  const sql = 'DELETE FROM actor_conversation_objects WHERE id=?'
  console.debug(sql, [id])
  const { success, error } = await db.prepare(sql).bind(id).run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}
