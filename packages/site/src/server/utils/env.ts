import type { MessageBody, NoteMessageBody } from '#shared/types/queue'

function env<T>(name: 'WS' | 'QUEUE' | 'QUEUE_NOTE' | 'QUEUE_PROFILE' | 'MAIL' | 'OTP' | 'DB' | 'BUCKET' | 'AI' | 'VECTORIZE_INDEX' | 'BACKBLAZE_KEY' | 'BACKBLAZE_KEY_ID' | 'BACKBLAZE_BUCKET_ID' | 'OPENAI_API_KEY' | 'USER_KEY' | 'SESSION_SECRET'): T {
  // @ts-expect-error globalThis.__env__ is injected by the runtime
  return process.env[name] || globalThis.__env__?.[name] || globalThis[name]
}

export function useEnv() {
  return {
    WS: env<Service>('WS'),
    QUEUE: env<Queue<MessageBody>>('QUEUE'),
    QUEUE_NOTE: env<Queue<NoteMessageBody>>('QUEUE_NOTE'),
    QUEUE_PROFILE: env<Queue<{ actorId: string }>>('QUEUE_PROFILE'),
    MAIL: env<Service>('MAIL'),
    OTP: env<KVNamespace>('OTP'),
    DB: env<D1Database>('DB'),
    BUCKET: env<R2Bucket>('BUCKET'),
    AI: env<Ai>('AI'),
    VECTORIZE_INDEX: env<VectorizeIndex>('VECTORIZE_INDEX'),
    BACKBLAZE_KEY: env<string>('BACKBLAZE_KEY'),
    BACKBLAZE_KEY_ID: env<string>('BACKBLAZE_KEY_ID'),
    BACKBLAZE_BUCKET_ID: env<string>('BACKBLAZE_BUCKET_ID'),
    OPENAI_API_KEY: env<string>('OPENAI_API_KEY'),
    USER_KEY: env<string>('USER_KEY'),
    SESSION_SECRET: env<string>('SESSION_SECRET'),
  }
}
