import { uniqBy } from 'lodash-es'
import { getActors, type Person } from '~/server/utils/backend/activitypub/actors'
import { loadLocalMastodonAccount } from '~/server/utils/backend/mastodon/account'
import { getActorFollowings, STATE_ACCEPTED, updateActorFollowing } from '~/server/utils/backend/mastodon/follow'
import { getActorTags, getTags } from '~/server/utils/backend/mastodon/hashtag'
import type { Featured, mastodon } from '#shared/types'
import { urlToHandle } from '~/server/utils/backend/utils/handle'
import { wsPublish } from '~/server/utils/backend/ws/stream'

export async function publishWs(type: string, actor: Person, status: mastodon.v1.Status, domain: string, db: D1Database, wsService: Service) {
  await wsPublish(wsService, `timeline:account:${urlToHandle(actor.id)}`, { type, status })

  const tagIds = status.tags.map(({ id }) => id)
  for (const tagId of tagIds) {
    await wsPublish(wsService, `timeline:tag:${tagId}`, { type, status })
  }

  await updateActorFollowing(db, { publishedDate: 'now' }, { targetActorId: actor.id, state: STATE_ACCEPTED })
  const actorFollowers = uniqBy(await getActorFollowings(db, { targetActorId: actor.id, state: STATE_ACCEPTED }), 'actorId')
  if (actorFollowers.length) {
    const followerActors = await getActors(db, { ids: actorFollowers.map(({ actorId }) => actorId) })
    for (const { id, actorId, publishedDate, readDate } of actorFollowers) {
      const followerActor = followerActors.find(({ id }) => id.toString() === actorId)
      if (followerActor) {
        const item: Featured = {
          id,
          publishedDate,
          readDate,
          account: await loadLocalMastodonAccount(db, actor, true),
        }
        await wsPublish(wsService, `timeline:featured:${urlToHandle(followerActor.id)}`, { type: 'update', item })
      }
    }
  }

  const actorTags = await getActorTags(db, { tagIds })
  const tags = await getTags(db, domain, { ids: tagIds })
  for (const { id, actorId, tagId, publishedDate, readDate } of actorTags) {
    const tag = tags.find(({ id }) => id === tagId)
    if (tag) {
      const item: Featured = {
        id,
        publishedDate,
        readDate,
        tag,
      }
      await wsPublish(wsService, `timeline:featured:${urlToHandle(actorId)}`, { type: 'update', item })
    }
  }
}
