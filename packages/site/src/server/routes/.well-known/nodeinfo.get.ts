export default defineEventHandler((event) => {
  const domain = getRequestDomain(event)
  setResponseHeader(event, 'cache-control', 'max-age=259200, public')

  return {
    links: [
      {
        rel: 'http://nodeinfo.diaspora.software/ns/schema/2.0',
        href: `https://${domain}/nodeinfo/2.0`,
      },
      {
        rel: 'http://nodeinfo.diaspora.software/ns/schema/2.1',
        href: `https://${domain}/nodeinfo/2.1`,
      },
    ],
  }
})
