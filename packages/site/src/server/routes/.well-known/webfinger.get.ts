import { parseHandle } from '~/server/utils/backend/utils/parse'
import { getActorById, actorURL } from '~/server/utils/backend/activitypub/actors'

export default defineEventHandler(async (event) => {
  const resource = getQuery(event).resource as string
  if (!resource) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Bad Request',
    })
  }

  const [scheme, query] = resource.split(':', 2)
  if (scheme !== 'acct' && query === undefined) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Bad Request',
    })
  }

  const { domain, localPart } = parseHandle(query)
  if (!domain) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Bad Request',
    })
  }

  const requestDomain = getRequestDomain(event)
  if (domain !== requestDomain) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Forbidden',
    })
  }

  const actor = await getActorById(useEnv().DB, actorURL(domain, localPart))
  if (actor === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not Found',
    })
  }

  const href = actor.id.toString()

  setResponseHeader(event, 'content-type', 'application/jrd+json')
  setResponseHeader(event, 'cache-control', 'max-age=259200, public')

  return {
    subject: `acct:${localPart}@${domain}`,
    aliases: [href],
    links: [
      {
        rel: 'self',
        type: 'application/activity+json',
        href,
      },
    ],
  }
})
