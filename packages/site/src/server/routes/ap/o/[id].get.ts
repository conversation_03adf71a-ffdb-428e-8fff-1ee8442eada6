import { getObjectById, uri } from '~/server/utils/backend/activitypub/objects'

export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const { id } = getRouterParams(event)

  const item = await getObjectById(useEnv().DB, uri(domain, id))
  if (item === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not Found',
    })
  }

  setResponseHeader(event, 'content-type', 'application/activity+json; charset=utf-8')

  return {
    '@context': [
      'https://www.w3.org/ns/activitystreams',
      {
        ostatus: 'http://ostatus.org#',
        atomUri: 'ostatus:atomUri',
        inReplyToAtomUri: 'ostatus:inReplyToAtomUri',
        conversation: 'ostatus:conversation',
        sensitive: 'as:sensitive',
        toot: 'http://joinmastodon.org/ns#',
        votersCount: 'toot:votersCount',
      },
    ],
    ...item,
  }
})
