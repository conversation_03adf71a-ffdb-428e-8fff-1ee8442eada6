import { parseHandle } from '~/server/utils/backend/utils/parse'
import { getActorById, actorURL } from '~/server/utils/backend/activitypub/actors'
import { getFollowingId } from '~/server/utils/backend/mastodon/follow'

export default defineEventHandler(async (event) => {
  const { id } = getRouterParams(event)
  const { domain, localPart } = parseHandle(id)
  const hostname = getRequestDomain(event)

  if (domain && domain !== hostname) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Forbidden',
    })
  }

  const actor = await getActorById(useEnv().DB, actorURL(hostname, localPart))
  if (actor === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not Found',
    })
  }

  const following = await getFollowingId(useEnv().DB, actor)

  setResponseHeader(event, 'content-type', 'application/activity+json; charset=utf-8')
  setResponseHeader(event, 'Cache-Control', 'max-age=180, public')

  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    'id': actor.following,
    'type': 'OrderedCollection',
    'totalItems': following.length,
    'first': new URL(actor.following + '/page'),
    'last': new URL(actor.following + '/page?min_id=0'),
  }
})
