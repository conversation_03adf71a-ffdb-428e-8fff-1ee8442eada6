import { z } from 'zod'
import { getActorById, propertiesSymbol, updateActor } from '~/server/utils/backend/activitypub/actors'
import { loadLocalMastodonAccount } from '~/server/utils/backend/mastodon/account'
import { Backblaze } from '~/server/utils/backend/media/backblaze'
import type { mastodon } from '#shared/types'

// https://docs.joinmastodon.org/methods/accounts
export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const person = await requireUserSession(event)
  const { display_name, note, avatar, header, ...theme } = await readValidatedFormData(event, z.object({
    display_name: z.string().optional(),
    note: z.string().optional(),
    avatar: z.instanceof(File).optional(),
    header: z.instanceof(File).optional(),
    theme_color_oklch: z.string().optional(),
    theme_font: z.enum(['sans', 'serif', 'mono']).optional(),
    theme_grid_gap: z.enum(['small', 'normal', 'big']).optional(),
    theme_corner_radius: z.enum(['none', 'small', 'big']).optional(),
  }).parse)

  const backblaze = new Backblaze(useEnv().BACKBLAZE_KEY_ID, useEnv().BACKBLAZE_KEY)

  const properties = person[propertiesSymbol]
  {
    if (display_name) {
      properties.name = display_name
    }
    if (note) {
      properties.summary = note
    }
    if (avatar) {
      const icon = await backblaze.upload(useEnv().BACKBLAZE_BUCKET_ID, { file: avatar })
      properties.icon = {
        url: `https://${domain}/media/original/${icon}`,
      }
    }
    if (header) {
      const icon = await backblaze.upload(useEnv().BACKBLAZE_BUCKET_ID, { file: header })
      properties.image = {
        url: `https://${domain}/media/original/${icon}`,
      }
    }
    const { theme_color_oklch, theme_font, theme_grid_gap, theme_corner_radius } = theme
    if (theme_color_oklch) {
      properties.theme = {
        ...(properties.theme ?? {}),
        colorOklch: theme_color_oklch.split(',').map(Number),
      }
    }
    if (theme_font) {
      properties.theme = {
        ...(properties.theme ?? {}),
        font: theme_font,
      }
    }
    if (theme_grid_gap) {
      properties.theme = {
        ...(properties.theme ?? {}),
        gridGap: theme_grid_gap,
      }
    }
    if (theme_corner_radius) {
      properties.theme = {
        ...(properties.theme ?? {}),
        cornerRadius: theme_corner_radius,
      }
    }
  }
  await updateActor(useEnv().DB, properties, person.id)

  // reload the current user and sent back updated infos
  {
    const actor = await getActorById(useEnv().DB, person.id)
    if (actor === null) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Not found',
      })
    }
    const user = await loadLocalMastodonAccount(useEnv().DB, actor, true)

    const res: mastodon.v1.AccountCredentials = {
      ...user,
      source: {
        note: user.note,
        fields: user.fields ?? [],
        privacy: 'public',
        sensitive: false,
        language: 'en',
        // follow_requests_count: 0,
      },
      role: {
        id: 0,
        name: 'user',
        color: '',
        position: 1,
        permissions: 0,
        highlighted: true,
        createdAt: '2022-09-08T22:48:07.983Z',
        updatedAt: '2022-09-08T22:48:07.983Z',
      },
    }

    const execute = async () => {
      // TODO send updates
      // const activity = activities.create(domain, person, actor)
      // await deliverFollowers(c.env.DB, c.env.USER_KEY, person, activity, c.env.QUEUE)
      await useEnv().QUEUE_PROFILE.send({ actorId: actor.id.toString() })
    }
    event.waitUntil(execute())
    return res
  }
})
