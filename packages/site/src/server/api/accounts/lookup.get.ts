import { z } from 'zod'
import { parseHand<PERSON> } from '~/server/utils/backend/utils/parse'
import { getAccount } from '~/server/utils/backend/accounts/getAccount'

// https://docs.joinmastodon.org/methods/accounts/#lookup
export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const { acct } = await getValidatedQuery(event, z.object({
    acct: z.string(),
  }).parse)

  const account = await getAccount(domain, acct, useEnv().DB)
  if (!account) {
    const handle = parseHandle(acct)
    throw createError({
      statusCode: 404,
      statusMessage: `Account ${handle.localPart}@${handle.domain} not found on ${domain}`,
    })
  }
  return account
})
