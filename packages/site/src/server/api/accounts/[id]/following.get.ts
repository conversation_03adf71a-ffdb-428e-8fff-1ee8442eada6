import { z } from 'zod'
import * as actors from '~/server/utils/backend/activitypub/actors'
import { actorURL } from '~/server/utils/backend/activitypub/actors'
import { getFollowing, loadActors } from '~/server/utils/backend/activitypub/actors/follow'
import { loadExternalMastodonAccount } from '~/server/utils/backend/mastodon/account'
import * as localFollow from '~/server/utils/backend/mastodon/follow'
import type { mastodon } from '#shared/types'
import { urlToHandle } from '~/server/utils/backend/utils/handle'
import type { Handle } from '~/server/utils/backend/utils/parse'
import { parseHandle } from '~/server/utils/backend/utils/parse'
import * as webfinger from '~/server/utils/backend/webfinger'

async function getRemoteFollowing(handle: Handle, db: D1Database) {
  const acct = `${handle.localPart}@${handle.domain}`
  const link = await webfinger.queryAcctLink(handle.domain!, acct)
  if (link === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }

  const actor = await actors.getAndCache(link, db)
  const followingIds = await getFollowing(actor)
  const following = await loadActors(db, followingIds)

  return Promise.all(following.map(actor => loadExternalMastodonAccount(urlToHandle(actor.id), actor, false)))
}

async function getLocalFollowing(domain: string, handle: Handle, db: D1Database) {
  const actorId = actorURL(domain, handle.localPart)
  const actor = await actors.getAndCache(actorId, db)

  const following = await localFollow.getFollowingId(db, actor)
  const out: Array<mastodon.v1.Account> = []

  for (let i = 0, len = following.length; i < len; i++) {
    const id = new URL(following[i])
    try {
      const actor = await actors.getAndCache(id, db)
      const acct = urlToHandle(actor.id)
      out.push(await loadExternalMastodonAccount(acct, actor))
    }
    catch (err) {
      if (err instanceof Error) {
        console.warn(`failed to retrieve following (${id}): ${err.message}`)
      }
    }
  }
  return out
}

// https://docs.joinmastodon.org/methods/accounts/#following
export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)

  const handle = parseHandle(id)
  if (handle.domain === null || (handle.domain !== null && handle.domain === domain)) {
    // Retrieve the infos from a local user
    return getLocalFollowing(domain, handle, useEnv().DB)
  }
  if (handle.domain !== null) {
    // Retrieve the infos of a remote actor
    return getRemoteFollowing(handle, useEnv().DB)
  }
  throw createError({
    statusCode: 403,
    statusMessage: '',
  })
})
