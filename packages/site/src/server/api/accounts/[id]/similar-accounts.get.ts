import { z } from 'zod'
import { getAccount } from '~/server/utils/backend/accounts/getAccount'
import { getActors } from '~/server/utils/backend/activitypub/actors'
import { EmbeddingsId } from '~/server/utils/backend/ai/embeddings'
import { similar } from '~/server/utils/backend/ai/similar'
import { loadLocalMastodonAccount } from '~/server/utils/backend/mastodon/account'
import type { mastodon } from '#shared/types'

// https://docs.joinmastodon.org/methods/accounts
export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)
  const { total } = await getValidatedQuery(event, z.object({
    total: z.number().default(5),
  }).parse)

  const account = await getAccount(domain, id, useEnv().DB)
  if (account === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  const ids = await similar(useEnv().DB, useEnv().VECTORIZE_INDEX, EmbeddingsId.fromActorUrl(account.id), total)

  const accounts: mastodon.v1.Account[] = []
  const actors = await getActors(useEnv().DB, { ids: ids.map(id => EmbeddingsId.toActorUrl(id)).filter((id): id is string => Boolean(id)) })
  for (const actor of actors) {
    const item = await loadLocalMastodonAccount(useEnv().DB, actor, true)
    if (item) {
      accounts.push(item)
    }
  }
  return accounts
})
