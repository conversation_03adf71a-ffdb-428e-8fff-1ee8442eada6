import { z } from 'zod'
import { getAccount } from '~/server/utils/backend/accounts/getAccount'
import * as UnfollowActivity from '~/server/utils/backend/activitypub/activities/unfollow'
import { deliverToActor } from '~/server/utils/backend/activitypub/deliver'
import { getSigningKey, toActor } from '~/server/utils/backend/mastodon/account'
import { removeFollowing } from '~/server/utils/backend/mastodon/follow'
import type { Featured, mastodon } from '#shared/types'
import { urlToHandle } from '~/server/utils/backend/utils/handle'
import { isLocalHandle } from '~/server/utils/backend/utils/parse'
import { wsPublish } from '~/server/utils/backend/ws/stream'

// https://docs.joinmastodon.org/methods/accounts/#unfollow
export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const person = await requireUserSession(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)

  const account = await getAccount(domain, id, useEnv().DB)
  if (account === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  const actor = toActor(account, domain)

  const execute = async () => {
    const actorFollowing = await removeFollowing(useEnv().DB, person, actor)
    if (actorFollowing) {
      if (!isLocalHandle(account.acct, domain)) {
        const activity = UnfollowActivity.create(person, actor)
        const signingKey = await getSigningKey(useEnv().USER_KEY, useEnv().DB, person)
        await deliverToActor(signingKey, person, actor, activity, domain)
      }
      const item: Featured = {
        id: actorFollowing.id,
        publishedDate: actorFollowing.publishedDate,
        readDate: actorFollowing.readDate,
        account,
      }
      await wsPublish(useEnv().WS, `timeline:featured:${urlToHandle(person.id)}`, { type: 'delete', item })
    }
  }
  event.waitUntil(execute())

  const res: mastodon.v1.Relationship = {
    id: account.id,
    following: false,
    requested: false,

    // FIXME: stub values
    showingReblogs: false,
    notifying: false,
    languages: [],
    followedBy: false,
    blocking: false,
    blockedBy: false,
    muting: false,
    mutingNotifications: false,
    domainBlocking: false,
    endorsed: false,
    requestedBy: false,
  }
  return res
})
