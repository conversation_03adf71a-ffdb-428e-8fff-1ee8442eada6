import { z } from 'zod'
import { getAccount } from '~/server/utils/backend/accounts/getAccount'

// https://docs.joinmastodon.org/methods/accounts
export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)

  const account = await getAccount(domain, id, useEnv().DB)
  if (account === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  return account
})
