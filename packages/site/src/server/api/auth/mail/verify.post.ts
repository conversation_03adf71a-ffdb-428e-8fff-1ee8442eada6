import { z } from 'zod'
import { create<PERSON><PERSON>, getPersonByEmail } from '~/server/utils/backend/activitypub/actors'
import { toMastodonAccount } from '~/server/utils/backend/mastodon/account'
import { urlToHandle } from '~/server/utils/backend/utils/handle'

export default defineEventHandler(async (event) => {
  const { email, ticket } = await readValidatedBody(event, z.object({
    email: z.string().email(),
    ticket: z.string(),
  }).parse)

  const oob = await useEnv().OTP.get(email)
  if (oob === null || oob !== ticket) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Invalid verification string',
    })
  }
  useEnv().OTP.delete(email)

  let person = await getPersonByEmail(useEnv().DB, email)
  if (!person) {
    person = await createPerson(getRequestDomain(event), useEnv().DB, useEnv().USER_KEY, email)
  }

  await setUserSession(event, {
    email,
  })
  return toMastodonAccount(urlToHandle(person.id), person)
})
