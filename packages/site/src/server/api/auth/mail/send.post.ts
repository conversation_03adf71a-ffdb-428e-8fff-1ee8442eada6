import { z } from 'zod'
import { VerifyMailRequest } from '~/server/utils/backend/mail'

const LIFETIME = 20 // minutes

function generateOtp(length: number): string {
  let otp = ''
  const randomValues = crypto.getRandomValues(new Uint8Array(length))
  // converting from 0-255 to single digits (0-9)
  for (let i = 0; i < randomValues.length; i++) {
    otp += Math.floor(randomValues[i] / 256 * 10)
  }
  return otp
}

export default defineEventHandler(async (event) => {
  const { email, url: verifyUrl } = await readValidatedBody(event, z.object({
    email: z.string().email(),
    url: z.string().url().optional(),
  }).parse)

  const url = getRequestURL(event)
  const otp = generateOtp(7)
  await useEnv().OTP.put(email, otp, { expirationTtl: 60000 * LIFETIME })
  return await useEnv().MAIL.fetch(url.href, new VerifyMailRequest(url, {
    ticket: otp,
    minutes: LIFETIME,
    email,
    url: verifyUrl ?? url.href,
  }))
})
