import { z } from 'zod'
import { createActorTag, getTag } from '~/server/utils/backend/mastodon/hashtag'
import type { Featured } from '#shared/types'
import { urlToHandle } from '~/server/utils/backend/utils/handle'
import { wsPublish } from '~/server/utils/backend/ws/stream'

// https://docs.joinmastodon.org/methods/tags
export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const actor = await requireUserSession(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)

  const tag = await getTag(useEnv().DB, domain, { id })
  if (tag === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  tag.following = true
  const execute = async () => {
    const actorTag = await createActorTag(useEnv().DB, actor, tag)
    if (actorTag) {
      const item: Featured = {
        id: actorTag.id,
        publishedDate: actorTag.publishedDate,
        readDate: actorTag.readDate,
        tag,
      }
      await wsPublish(useEnv().WS, `timeline:featured:${urlToHandle(actor.id)}`, { type: 'create', item })
    }
  }
  event.waitUntil(execute())
  return tag
})
