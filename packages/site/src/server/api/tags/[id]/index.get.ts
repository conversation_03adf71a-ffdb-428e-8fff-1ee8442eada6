// https://docs.joinmastodon.org/methods/tags

import { z } from 'zod'
import { countActorTag, getTag } from '~/server/utils/backend/mastodon/hashtag'

export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)

  const tag = await getTag(useEnv().DB, domain, { id, history: true })
  if (tag === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  const actor = await getUserSession(event)
  if (actor) {
    const count = await countActorTag(useEnv().DB, actor, tag)
    tag.following = count === null ? false : (count > 0)
  }
  return tag
})
