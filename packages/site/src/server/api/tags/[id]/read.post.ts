// https://docs.joinmastodon.org/methods/tags

import { z } from 'zod'
import { getTag, updateActorTags } from '~/server/utils/backend/mastodon/hashtag'
import type { Featured } from '#shared/types'
import { urlToHandle } from '~/server/utils/backend/utils/handle'
import { wsPublish } from '~/server/utils/backend/ws/stream'

export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const actor = await requireUserSession(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)

  const tag = await getTag(useEnv().DB, domain, { id })
  if (tag === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  const execute = async () => {
    const actorTag = await updateActorTags(useEnv().DB, { readDate: 'now' }, { tagId: tag.id, actorId: actor.id })
    if (actorTag) {
      const item: Featured = {
        id: actorTag.id,
        publishedDate: actorTag.publishedDate,
        readDate: actorTag.readDate,
        tag,
      }
      await wsPublish(useEnv().WS, `timeline:featured:${urlToHandle(actor.id)}`, { type: 'update', item })
    }
  }
  event.waitUntil(execute())
  return tag
})
