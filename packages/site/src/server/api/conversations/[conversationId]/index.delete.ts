import { z } from 'zod'
import { deleteConversation } from '~/server/utils/backend/mastodon/conversations'

export default defineEventHandler(async (event) => {
  const person = await requireUserSession(event)
  const { conversationId } = await getValidatedRouterParams(event, z.object({
    conversationId: z.string(),
  }).parse)

  async function execute() {
    deleteConversation(useEnv().DB, person.id.toString(), conversationId)
  }
  event.waitUntil(execute())
  return {}
})
