import { z } from 'zod'
import { deleteMessageInConversation } from '~/server/utils/backend/mastodon/conversations'
import { Backblaze } from '~/server/utils/backend/media/backblaze'

export default defineEventHandler(async (event) => {
  const person = await requireUserSession(event)
  const { conversationId, messageId } = await getValidatedRouterParams(event, z.object({
    conversationId: z.string(),
    messageId: z.string(),
  }).parse)
  const bz = new Backblaze(useEnv().BACKBLAZE_KEY_ID, useEnv().BACKBLAZE_KEY)
  return deleteMessageInConversation(useEnv().DB, useEnv().WS, bz, useEnv().BUCKET, conversationId, person, messageId)
})
