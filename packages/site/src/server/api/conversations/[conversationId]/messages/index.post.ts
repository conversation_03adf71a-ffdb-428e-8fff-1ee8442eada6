import { z } from 'zod'
import { addMessageToConversation, newConversation } from '~/server/utils/backend/mastodon/conversations'

export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const person = await requireUserSession(event)
  const { conversationId } = await getValidatedRouterParams(event, z.object({
    conversationId: z.string(),
  }).parse)
  const { content, publishId } = await readValidatedBody(event, z.object({
    content: z.string().trim(),
    publishId: z.string(),
  }).parse)

  const newConversationId = await newConversation(useEnv().DB, domain, person, conversationId)
  return addMessageToConversation(useEnv().DB, useEnv().WS, newConversationId, person, content, publishId)
})
