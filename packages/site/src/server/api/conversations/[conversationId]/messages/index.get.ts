import { z } from 'zod'
import { getActorConversationsBy } from '~/server/utils/backend/entities/conversation/actors'
import { getConversationMessagesBy } from '~/server/utils/backend/entities/conversation/message'
import { urlToHandle } from '~/server/utils/backend/utils/handle'
import { toMastodonAccount } from '~/server/utils/backend/mastodon/account'
import type { ConversationMessage } from '#shared/types'

export default defineEventHandler(async (event) => {
  const person = await requireUserSession(event)
  const { conversationId } = await getValidatedRouterParams(event, z.object({
    conversationId: z.string(),
  }).parse)
  const [actorConversation] = await getActorConversationsBy(useEnv().DB, { topicId: conversationId, actorId: person.id.toString() })
  if (!actorConversation) {
    return []
  }

  const rows = await getConversationMessagesBy(useEnv().DB, { topicId: conversationId })
  return rows.map<ConversationMessage>(({ actor, ...row }) => ({
    ...row,
    account: toMastodonAccount(urlToHandle(actor.id), actor),
    state: 'delivered',
  }))
})
