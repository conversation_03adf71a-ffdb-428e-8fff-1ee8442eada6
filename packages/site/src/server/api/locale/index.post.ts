import { z } from 'zod'
import { defaultLocale, locales } from '~/server/utils/backend/locale'

const NAME = 'locale'

export default defineEventHandler(async (event) => {
  const { locale } = await readValidatedBody(event, z.object({
    locale: z.enum([defaultLocale, ...Object.keys(locales).filter(name => name !== defaultLocale)]),
  }).parse)
  setCookie(event, NAME, locale)
  return { locale }
})
