import { z } from 'zod'
import { PUBLIC_GROUP } from '~/server/utils/backend/activitypub/activities'
import { addObjectInOutbox } from '~/server/utils/backend/activitypub/actors/outbox'
import type { APObject, Document } from '~/server/utils/backend/activitypub/objects'
import { getObjectByMastodonId, mastodonIdSymbol, originalObjectIdSymbol } from '~/server/utils/backend/activitypub/objects'
import { createNote, type Note } from '~/server/utils/backend/activitypub/objects/note'
import * as errors from '~/server/utils/backend/errors'
import * as idempotency from '~/server/utils/backend/mastodon/idempotency'
import { toMastodonStatusFromObject } from '~/server/utils/backend/mastodon/status'

export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const actor = await requireUserSession(event)

  const { status, visibility, media_ids, in_reply_to_id, publish_id, gridSize } = await readValidatedBody(event, z.object({
    status: z.string().optional(),
    visibility: z.enum(['public', 'unlisted', 'private', 'direct', 'draft']).default('public'),
    sensitive: z.boolean().default(false),
    media_ids: z.string().array().default([]),
    in_reply_to_id: z.string().optional(),
    publish_id: z.string().optional(),
    gridSize: z.enum(['horizontal', 'vertical', 'large']).optional(),
  }).parse)

  const idempotencyKey = getRequestHeader(event, 'Idempotency-Key') ?? null
  if (idempotencyKey !== null) {
    const maybeObject = await idempotency.hasKey(useEnv().DB, idempotencyKey)
    if (maybeObject !== null) {
      return toMastodonStatusFromObject(useEnv().DB, maybeObject as Note, domain)
    }
  }

  const mediaAttachments: Array<Document> = []
  if (media_ids && media_ids.length > 0) {
    if (media_ids.length > 4) {
      return errors.exceededLimit('up to 4 images are allowed')
    }
    for (let i = 0, len = media_ids.length; i < len; i++) {
      const id = media_ids[i]
      const document = await getObjectByMastodonId(useEnv().DB, id)
      if (document === null) {
        console.warn('object attachement not found: ' + id)
        continue
      }
      mediaAttachments.push(document)
    }
  }

  let inReplyToObject: APObject | null = null
  if (in_reply_to_id) {
    inReplyToObject = await getObjectByMastodonId(useEnv().DB, in_reply_to_id)
    if (inReplyToObject === null) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Not found',
      })
    }
  }
  const extraProperties: Record<string, unknown> = { gridSize, publishId: publish_id }
  if (inReplyToObject !== null) {
    extraProperties.inReplyTo = inReplyToObject[originalObjectIdSymbol] || inReplyToObject.id.toString()
  }

  // const mentions = status ? await getMentions(status, domain, useEnv().DB) : []
  // if (mentions.length > 0) {
  //   extraProperties.tag = mentions.map(newMention)
  // }
  // const content = status ? enrichStatus(status, mentions) : ''
  const content = status ?? ''

  let to: string[] = []
  let cc: string[] = []
  if (visibility === 'public') {
    to = [PUBLIC_GROUP]
    cc = [actor.followers?.toString()]
  }
  else if (visibility === 'direct') {
    to = /* mentions.map((a) => a.id.toString()) */[]
  }
  else {
    return errors.validationError(`status with visibility: ${visibility}`)
  }
  const note = await createNote(domain, useEnv().DB, content, actor, to, cc, mediaAttachments, extraProperties)
  const res = await toMastodonStatusFromObject(useEnv().DB, note as Note, domain, publish_id)

  const execute = async () => {
    // if (inReplyToObject !== null) {
    //   // after the status has been created, record the reply.
    //   await insertReply(useEnv().DB, actor, note, inReplyToObject)
    // }

    // const activity = activities.delete.create(domain, actor, note)
    // await deliverFollowers(useEnv().DB, useEnv().USER_KEY, actor, activity, useEnv().QUEUE)

    if (visibility === 'public') {
      await addObjectInOutbox(useEnv().DB, actor.id.toString(), note.id.toString())

      // A public note is sent to the public group URL and cc'ed any mentioned actors.
      // for (let i = 0, len = mentions.length; i < len; i++) {
      //   const targetActor = mentions[i]
      //   note.cc.push(targetActor.id.toString())
      // }
    }
    else if (visibility === 'direct') {
      //  A direct note is sent to mentioned people only
      // for (let i = 0, len = mentions.length; i < len; i++) {
      //   const targetActor = mentions[i]
      //   await addObjectInOutbox(useEnv().DB, actor.id.toString(), note.id.toString(), undefined, targetActor.id.toString())
      // }
    }

    // {
    //   // If the status is mentioning other persons, we need to delivery it to them.
    //   for (let i = 0, len = mentions.length; i < len; i++) {
    //     const targetActor = mentions[i]
    //     const activity = activities.delete.create(domain, actor, note)
    //     const signingKey = await getSigningKey(useEnv().USER_KEY, useEnv().DB, actor)
    //     await deliverToActor(signingKey, actor, targetActor, activity, domain)
    //   }
    // }
    if (idempotencyKey !== null) {
      await idempotency.insertKey(useEnv().DB, idempotencyKey, note)
    }

    if (res) {
      await publishWs('create', actor, res, domain, useEnv().DB, useEnv().WS)
      await useEnv().QUEUE_NOTE.send({ type: 'create', mastodonId: note?.[mastodonIdSymbol], actorId: actor.id.toString() })
    }
  }
  event.waitUntil(execute())
  return res
})
