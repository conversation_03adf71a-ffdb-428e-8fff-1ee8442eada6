import { z } from 'zod'
import { getObjectByMastodonId } from '~/server/utils/backend/activitypub/objects'
import { updateNote, type Note } from '~/server/utils/backend/activitypub/objects/note'
import { toMastodonStatusFromObject } from '~/server/utils/backend/mastodon/status'
import { Backblaze } from '~/server/utils/backend/media/backblaze'
import { urlToHandle } from '~/server/utils/backend/utils/handle'

export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const actor = await requireUserSession(event)
  const { id: mastodonId } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)
  const { status: content, gridSize } = await readValidatedBody(event, z.object({
    status: z.string().optional(),
    spoiler_text: z.string().optional(),
    sensitive: z.boolean().default(false),
    language: z.string().optional(),
    gridSize: z.enum(['horizontal', 'vertical', 'large', 'normal']).optional().nullable(),
  }).parse)

  let obj = await getObjectByMastodonId(useEnv().DB, mastodonId)
  if (obj === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  let status = await toMastodonStatusFromObject(useEnv().DB, obj as Note, domain)
  if (status === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  if (status.account.id !== urlToHandle(actor.id)) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }

  const mediaIds = await updateNote(useEnv().DB, obj, content, gridSize)

  obj = await getObjectByMastodonId(useEnv().DB, mastodonId)
  status = await toMastodonStatusFromObject(useEnv().DB, obj as Note, domain)
  if (obj === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }

  const execute = async () => {
    if (mediaIds.length) {
      const bz = new Backblaze(useEnv().BACKBLAZE_KEY_ID, useEnv().BACKBLAZE_KEY)
      for (const mediaId of mediaIds) {
        bz.delete(mediaId)
        useEnv().BUCKET.delete(mediaId)
      }
    }
    if (status) {
      await publishWs('update', actor, status, domain, useEnv().DB, useEnv().WS)
      await useEnv().QUEUE_NOTE.send({ type: 'update', mastodonId, actorId: actor.id.toString() })
    }
  }
  event.waitUntil(execute())
  return status
})
