import { z } from 'zod'
import * as LikeActivity from '~/server/utils/backend/activitypub/activities/like'
import { getAndCache } from '~/server/utils/backend/activitypub/actors'
import { deliverToActor } from '~/server/utils/backend/activitypub/deliver'
import { getObjectByMastodonId, originalActorIdSymbol, originalObjectIdSymbol } from '~/server/utils/backend/activitypub/objects'
import type { Note } from '~/server/utils/backend/activitypub/objects/note'
import { getSigningKey } from '~/server/utils/backend/mastodon/account'
import { insertLike } from '~/server/utils/backend/mastodon/like'
import { toMastodonStatusFromObject } from '~/server/utils/backend/mastodon/status'

export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const person = await requireUserSession(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)

  const obj = await getObjectByMastodonId(useEnv().DB, id)
  if (obj === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }

  await insertLike(useEnv().DB, person, obj.id.toString())

  const status = await toMastodonStatusFromObject(useEnv().DB, obj as Note, domain)
  if (status === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  status.favourited = true

  const execute = async () => {
    const { [originalObjectIdSymbol]: objectId, [originalActorIdSymbol]: actorId } = obj
    if (objectId && actorId) {
      // Liking an external object delivers the like activity
      const targetActor = await getAndCache(new URL(objectId), useEnv().DB)
      if (targetActor) {
        const activity = LikeActivity.create(person, new URL(actorId))
        const signingKey = await getSigningKey(useEnv().USER_KEY, useEnv().DB, person)
        await deliverToActor(signingKey, person, targetActor, activity, domain)
      }
    }
  }
  event.waitUntil(execute())
  return status
})
