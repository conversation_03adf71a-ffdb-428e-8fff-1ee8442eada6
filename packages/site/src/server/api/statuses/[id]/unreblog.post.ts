import { z } from 'zod'
import * as AnnounceActivity from '~/server/utils/backend/activitypub/activities/announce'
import * as UndoActivity from '~/server/utils/backend/activitypub/activities/undo'
import { getAndCache } from '~/server/utils/backend/activitypub/actors'
import { deliverFollowers, deliverToActor } from '~/server/utils/backend/activitypub/deliver'
import { getObjectByMastodonId, originalActorIdSymbol, originalObjectIdSymbol } from '~/server/utils/backend/activitypub/objects'
import type { Note } from '~/server/utils/backend/activitypub/objects/note'
import { getSigningKey } from '~/server/utils/backend/mastodon/account'
import { removeReblog } from '~/server/utils/backend/mastodon/reblog'
import { toMastodonStatusFromObject } from '~/server/utils/backend/mastodon/status'

export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const person = await requireUserSession(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)

  await removeReblog(useEnv().DB, person, id)

  const obj = await getObjectByMastodonId(useEnv().DB, id)
  if (obj === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  const status = await toMastodonStatusFromObject(useEnv().DB, obj as Note, domain)
  if (status === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  status.reblogged = false

  const execute = async () => {
    const { [originalObjectIdSymbol]: objectId, [originalActorIdSymbol]: actorId } = obj
    if (objectId && actorId) {
      // Rebloggin an external object delivers the announce activity to the post author.
      const targetActor = await getAndCache(new URL(actorId), useEnv().DB)
      if (!targetActor) {
        const activity = UndoActivity.create(person, AnnounceActivity.create(person, new URL(objectId)))
        const signingKey = await getSigningKey(useEnv().USER_KEY, useEnv().DB, person)
        await Promise.all([
          // Delivers the announce activity to the post author.
          deliverToActor(signingKey, person, targetActor, activity, domain),
          // Share reblogged by delivering the announce activity to followers
          deliverFollowers(useEnv().DB, useEnv().USER_KEY, person, activity, useEnv().QUEUE),
        ])
      }
    }
  }
  event.waitUntil(execute())
  return status
})
