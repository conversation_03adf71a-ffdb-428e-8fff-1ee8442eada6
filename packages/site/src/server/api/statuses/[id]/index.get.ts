import { z } from 'zod'
import { getMastodonStatusById } from '~/server/utils/backend/mastodon/status'

export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)

  const status = await getMastodonStatusById(useEnv().DB, id, domain)
  if (status === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  return status
})
