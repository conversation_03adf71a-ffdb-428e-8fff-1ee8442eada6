import { z } from 'zod'
import { getObjectByMastodonId } from '~/server/utils/backend/activitypub/objects'
import { getDescendants } from '~/server/utils/backend/mastodon/reply'
import type { mastodon } from '#shared/types'

// https://docs.joinmastodon.org/methods/statuses
export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const actor = await getUserSession(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)

  const obj = await getObjectByMastodonId(useEnv().DB, id)
  if (obj === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }

  const descendants = await getDescendants(domain, useEnv().DB, obj, actor)
  const out: mastodon.v1.Context = {
    ancestors: [],
    descendants,
  }
  return out
})
