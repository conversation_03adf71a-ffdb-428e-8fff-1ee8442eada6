import { z } from 'zod'
import { getObjectByMastodonId, getObjectsBy } from '~/server/utils/backend/activitypub/objects'
import type { Note } from '~/server/utils/backend/activitypub/objects/note'
import { similar } from '~/server/utils/backend/ai/similar'
import { toMastodonStatusFromObject } from '~/server/utils/backend/mastodon/status'
import type { mastodon } from '#shared/types'

export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)
  const { total } = await getValidatedQuery(event, z.object({
    total: z.number().default(5),
  }).parse)

  const obj = await getObjectByMastodonId(useEnv().DB, id)
  if (obj === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  const mastodonIds = await similar(useEnv().DB, useEnv().VECTORIZE_INDEX, id, total)
  const statuses: mastodon.v1.Status[] = []
  const objects = await getObjectsBy(useEnv().DB, { mastodonIds })
  for (const object of objects) {
    const status = await toMastodonStatusFromObject(useEnv().DB, object as Note, domain)
    if (status) {
      statuses.push(status)
    }
  }
  return statuses
})
