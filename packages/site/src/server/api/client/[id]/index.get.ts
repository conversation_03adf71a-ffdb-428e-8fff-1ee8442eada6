import { z } from 'zod'
import { getClientById } from '~/server/utils/backend/mastodon/client'

export default defineEventHandler(async (event) => {
  await requireUserSession(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)

  const client = await getClientById(useEnv().DB, id)
  if (client === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }
  return client
})
