// https://docs.joinmastodon.org/methods/timelines

import { z } from 'zod'
import { getTagTimeline } from '~/server/utils/backend/mastodon/timeline'

export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const person = await requireUserSession(event)

  const { tagId } = await getValidatedRouterParams(event, z.object({
    tagId: z.string(),
  }).parse)
  const { max_id: maxId, offset } = await getValidatedQuery(event, z.object({
    max_id: z.string().optional(),
    offset: z.number().default(0),
  }).parse)
  if (maxId) {
    return []
  }
  return getTagTimeline(domain, useEnv().DB, tagId, offset, person)
})
