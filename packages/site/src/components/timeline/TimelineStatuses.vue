<script setup lang="ts">
import type { mastodon } from '#shared/types'

const props = defineProps<{
  url: string
  channel: string
}>()
const { url, channel } = toRefs(props)

const { stream } = useStreaming<{ type: string, status: mastodon.v1.Status }>(channel)
const { items, status, error, loadNext } = await usePaginatorFetch<mastodon.v1.Status>(
  toValue(channel),
  url,
  'uri',
  computed(() => ({ type: stream.value?.type, payload: stream.value?.status })),
  (eventType, status) => {
    if (eventType === 'delete') {
      removeCachedStatus(status.id)
    }
    else {
      cacheStatus(status, true)
    }
  },
  (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
)
const endAnchor = useEndAnchor(loadNext)
</script>

<template>
  <div class="grid-container">
    <CommonPaginator :items="items" class="grid" v-bind="{ keyProp: 'id', pending: status === 'pending', error }">
      <template #default="{ item }">
        <StatusCard :status="item" />
      </template>
      <template #done>
        <div ref="endAnchor" />
      </template>
    </CommonPaginator>
  </div>
</template>

<style>
.status-wrap {
  position: relative;
  transition: opacity 0.25s;
}

.grid:hover {
  .status-wrap:not(:hover)::after {
    content: '';
    display: block;
    position: absolute;
    inset: 0;
    z-index: 100;
    pointer-events: none;
    background-color: var(--color-panel-minor);
    mix-blend-mode: multiply;
    opacity: 0.3;
  }
}
</style>
