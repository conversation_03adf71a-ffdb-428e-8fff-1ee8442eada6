<script setup>
const props = defineProps({
  gap: { type: String, default: 'normal' },
})
const emit = defineEmits(['update:gap'])
</script>

<template>
  <div class="button-block theme-grid">
    <button
      v-for="n in ['small', 'normal', 'big']"
      :key="n"
      class="large compact"
      :class="{ active: props.gap === n }"
      @click="emit('update:gap', n)">
      <div class="grid" :class="[n, { active: props.gap === n }]">
        <span /><span /><span /><span />
      </div>
    </button>
  </div>
</template>

<style lang='scss' scoped>
.theme-corner-radius-none  .theme-grid {
  --border-r: 0;
}
.theme-corner-radius-small .theme-grid {
  --border-r: 0.12rem;
}
.theme-corner-radius-big .theme-grid {
  --border-r: 0.24rem;
}

.theme-grid {
  display: flex;
  gap: var(--padding-base);
  justify-content: center;

  & > button {
    height: var(--base-size);
  }
  .grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    width: 55%;
    // height: 55%;
    aspect-ratio: 1 / 1;
    padding: 0;

    & > * {
      display: block;
      width: 100%;
      height: 100%;
      background-color: #747474;
      border-radius: var(--border-r);
    }

    &.active > * {
      background-color: var(--color-text-working);
    }

    &.small {
      gap: 0.0625rem; // 1px
    }
    &.normal {
      gap: 0.1875rem; // 3px
    }
    &.big {
      gap: 0.3125rem; // 5px
    }
  }
}
</style>
