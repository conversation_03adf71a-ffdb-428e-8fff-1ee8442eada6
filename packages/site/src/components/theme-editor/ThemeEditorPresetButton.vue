<script setup lang="ts">
import type { mastodon } from '#shared/types'

const props = defineProps<{
  theme: mastodon.v1.AccountTheme
  active: boolean
}>()

const theme = useTheme(computed(() => props.theme))
const abc = ['A', 'B', 'C', 'D']
</script>

<template>
  <button class="theme-editor-preset-button theme" :disabled="active" :class="{ active }" :style="theme">
    <div class="preset-preview">
      <div class="title">
        Abcd
      </div>
      <div class="panels">
        <div v-for="l in abc" :key="l">
          <span>{{ l }}</span>
        </div>
      </div>
    </div>
  </button>
</template>

<style>
.theme-editor-preset-button {
  --gap-scale: 0.2;
  background-color: var(--color-bg);
  padding: calc(var(--grid-gap) * var(--gap-scale));
  border-radius: 10px;

  &.active {
    box-shadow: 0 0 0 2px black;
    opacity: 1;
  }

  .title {
    color: var(--color-accent);
    margin-bottom: var(--padding-mini);
  }

  .panels {
    --_size: var(--padding-big);
    display: grid;
    grid-template-columns: var(--_size) var(--_size);
    /* grid-template-rows: var(--_size) 1fr; */
    gap: calc(var(--grid-gap) * var(--gap-scale));

    &>* {
      display: grid;
      place-content: center;
      background-color: var(--color-panel);
      color: var(--color-panel-text);
      border-radius: calc(var(--corner-radius) * 0.25);
      padding: 2px;
      font-size: calc(var(--font-size-body) * 0.35);
      aspect-ratio: 1;
    }
  }
}
</style>
