<script setup lang="ts">
import Draggable from 'vuedraggable'
import type { IndiPoll } from '@/composables/tiptap/poll'

const props = defineProps<{
  modelValue: IndiPoll
}>()

const model = reactive({ ...props.modelValue })
const emit = defineEmits(['update:modelValue'])
const tempPoll = reactive<IndiPoll>({})

const pollTitle = computed({
  get: () => tempPoll.title ?? model.title,
  set: (newVal) => {
    tempPoll.title = newVal
    // emit('update:title', newVal)
  },
})
const pollOptions = computed({
  get: () => tempPoll.options ?? model.options,
  set: (newVal) => {
    tempPoll.options = newVal
    // emit('update:options', newVal)
  },
})

function submit() {
  emit('update:modelValue', tempPoll)
}
</script>

<template>
  <div class="tiptap-poll-editor">
    <InputBox
      v-model="pollTitle"
      placeholder="Ask a question"
      type="text"
      name="question"
      autocomplete="off"
      autofocus
      required
    />
    <Draggable v-model="pollOptions" tag="ul" item-key="id">
      <template #item="{ element }">
        <li>{{ element.title }}</li>
      </template>
    </Draggable>
  </div>
  <button @click="submit">
    Save
  </button>
</template>

<style lang='scss'>
.tiptap-poll-editor{
  position: relative;
}
</style>
