<script setup lang="ts">
import type { Editor } from '@tiptap/vue-3'

const props = defineProps<{
  editor: Editor
}>()

const popup = ref()
const _href = ref<string | undefined>()
const inputBox = ref()

const href = computed({
  get: () => { return _href.value ?? props.editor?.getAttributes('link').href },
  set: (newValue: string) => {
    _href.value = newValue
  },
})

function setLink() {
  if (href.value) {
    if (href.value.split('://').length === 1) {
      href.value = 'https://' + href.value
    }
    props.editor?.chain().focus().setLink({ href: href.value }).run()
  }
  else {
    props.editor?.chain().focus().unsetLink().run()
  }
  popup.value?.close()
}

function onStateChanged(state: { isVisible: boolean }) {
  if (state.isVisible) {
    inputBox.value?.focus()
  }
  else {
    // props.editor?.chain().focus().run()
  }
}
</script>

<template>
  <div class="link-tools" :class="{ open: !!href }">
    <UiPopup
      ref="popup"
      :arrow="true"
      theme="indi-light"
      placement="bottom"
      @state="onStateChanged">
      <template #title>
        <span class="icon-link" />
      </template>
      <div>
        <InputBox
          ref="inputBox"
          v-model="href"
          placeholder="https://"
          type="url"
          @change="setLink">
          <template #add-on>
            <button class="compact" @click="setLink">
              <span class="icon-check" />
            </button>
          </template>
        </InputBox>
      </div>
    </UiPopup>
    <a v-if="href" :href="href" target="_blank">
      <span class="icon-external-link" />
    </a>
  </div>
</template>

<style lang='scss' scoped>
.link-tools {
  display: flex;
  align-items: center;
  height: 100%;
  aspect-ratio: 1 / 1;
  transition: aspect-ratio 0.25s;

  &.open {
    aspect-ratio: 2 / 1;
  }

  & > * {
    display: block;
    height: 100%;
    aspect-ratio: 1 / 1;
  }
  a {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.tippy-popup {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
