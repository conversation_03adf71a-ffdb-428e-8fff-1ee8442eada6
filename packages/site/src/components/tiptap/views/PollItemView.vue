<script setup lang="ts">
import { NodeViewWrapper, NodeViewContent } from '@tiptap/vue-3'
</script>

<template>
  <NodeViewWrapper as="li" dir="auto" class="poll-item">
    <label>
      <input type="checkbox">
      <NodeViewContent />
    </label>
  </NodeViewWrapper>
</template>

<style lang="scss">
.poll-item {
  label {
    display: flex;
    align-items: center;
    gap: var(--padding-base);
  }

  input[type="checkbox"] {
    --box-size: var(--size-two-third);
    appearance: none;
    background-color: transparent;
    margin: 0;
    padding: 0;
    font: inherit;
    display: grid;
    place-content: center;
    color: currentColor;
    width: var(--box-size);
    height: var(--box-size);
    border: 0.15em solid currentColor;
    border-radius: 900px;

    &::before {
      --icon-size: calc(var(--box-size) * 0.75);
      --icon-url: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="24" height="24" viewBox="0 0 24 24"%3E%3Cpath fill="currentColor" d="m9.55 18l-5.7-5.7l1.425-1.425L9.55 15.15l9.175-9.175L20.15 7.4L9.55 18Z"%2F%3E%3C%2Fsvg%3E');
      content: "";
      width: var(--icon-size);
      height: var(--icon-size);
      transform: scale(0);
      transition: 50ms transform ease-in-out;
      transform-origin: bottom left;
      display: inline-block;
      mask: var(--icon-url) no-repeat;
      mask-size: 100% 100%;
      background-color: currentColor;
      color: inherit;
    }

    &:checked {
      background-color: currentColor;

      &::before {
        transform: scale(1);
        background-color: white;
      }
    }

    &:focus {
      outline: max(2px, 0.15em) solid currentColor;
      outline-offset: 0; //max(2px, 0.15em);
    }
  }

  &:first-child {
    input {
      display: none;
    }

    font-weight: 600;
    font-size: var(--font-size-large, var(--h4));
  }
}
</style>
