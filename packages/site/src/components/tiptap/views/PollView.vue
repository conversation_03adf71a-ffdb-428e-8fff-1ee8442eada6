<script setup lang="ts">
import { NodeViewWrapper, nodeViewProps } from '@tiptap/vue-3'
import type { IndiPoll, IndiPollOption } from '@/composables/tiptap/poll'

const props = defineProps(nodeViewProps)

const editable = useState('isEditable')
const expired = computed(() => {
  return props.node.attrs.expiresAt && (new Date(props.node.attrs.expiresAt).getTime()) < (new Date().getTime())
})

const allowPollEdit = computed(() => {
  return editable && !expired.value && !props.node.attrs.votesCount
})

const poll = computed<IndiPoll>({
  get: () => props.node.attrs,
  set: (newVal: IndiPoll) => {
    Object.entries(newVal).forEach(([key, value]) => {
      return props.updateAttributes({ [key]: value })
    })
  },
})

const title = computed({
  get: () => props.node.attrs.title,
  set: (title: string) => {
    props.updateAttributes({ title })
  },
})

const options = computed<IndiPollOption[]>({
  get: () => /* props.node.attrs.options ?? */ [],
  set: (options: IndiPollOption[]) => {
    props.updateAttributes({ options })
  },
})

const invalidPoll = computed(() => !title.value || !options.value.length)

const requestOpenEditor = ref(false)
const requestCloseEditor = ref(false)

const shouldOpenEditor = computed(() => {
  if (requestCloseEditor.value) { return false }
  return allowPollEdit.value && (requestOpenEditor.value || invalidPoll.value)
})

function handleCloseRequest() {
  requestCloseEditor.value = true
  requestOpenEditor.value = false
}
function handleOpenRequest() {
  requestOpenEditor.value = true
  requestCloseEditor.value = false
}
</script>

<template>
  <NodeViewWrapper
    data-type="poll"
    class="poll-wrap222">
    <header class="poll-header">
      <span>{{ poll.title }}</span>
      <button class="compact" @click="handleOpenRequest">
        <span class="icon-pencil" />
      </button>
    </header>
    <ul data-type="poll">
      <li v-for="option in poll.options" :key="option.id">
        <span class="option-title">{{ option.title }}</span>
        <span v-if="option.votesCount" class="option-votes-count">{{ option.votesCount }}</span>
      </li>
    </ul>
    <UiPopoverWidget
      :open="shouldOpenEditor"
      @request-close="handleCloseRequest">
      <template #header-right />
      <TiptapPollEditor v-model="poll" />
    </UiPopoverWidget>
  </NodeViewWrapper>
</template>
