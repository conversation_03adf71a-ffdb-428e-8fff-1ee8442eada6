<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'

const props = defineProps(nodeViewProps)
</script>

<template>
  <NodeViewWrapper>
    <div class="content-embed-editor-wrapper">
      <ContentEmbed :url="props.node.attrs.href" />
      <button type="button" @click="props.deleteNode"><span class="icon-close small" /></button>
    </div>
  </NodeViewWrapper>
</template>

<style scoped>
.content-embed-editor-wrapper {
  --embed-bg-color: #DFDFDF;
  position: relative;
  a {
    text-decoration: none;
  }
  button {
    padding: 0;
    background-color: transparent;
    border-radius: calc(var(--corner-radius) - var(--padding-mini));
    position: absolute;
    top: var(--padding-mini);
    right: var(--padding-mini);
    background-color: color-mix(in lab, var(--embed-bg-color) 30%, transparent);
    backdrop-filter: blur(10px);
  }
}
</style>
