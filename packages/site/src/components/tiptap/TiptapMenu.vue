<script setup lang="ts">
import type { Editor } from '@tiptap/vue-3'

const props = withDefaults(defineProps<{
  editor: Editor | null
  isEditable: boolean
}>(), {
  editor: null,
})

function isButtonActive(type: string) {
  return props.editor?.isActive(type)
}

async function pickMedia() {
  for (const item of await pickContentMedia()) {
    props.editor?.chain().focus().setAttachment(item).run()
  }
}
</script>

<template>
  <div class="tiptap-menu">
    <template v-if="isEditable">
      <button
        type="button"
        :class="{ active: isButtonActive('bold') }"
        @click="editor?.chain().focus().toggleBold().run()">
        <span class="icon-format-bold" />
      </button>

      <button
        type="button"
        :class="{ active: isButtonActive('italic') }"
        @click="editor?.chain().focus().toggleItalic().run()">
        <span class="icon-format-italic" />
      </button>

      <div class="tiptap-media-uploader">
        <button
          type="button"
          class="figure-menu-button"
          :class="{ active: isButtonActive('media-viewer') }"
          @click="pickMedia">
          <span class="icon-image" />
        </button>
      </div>

      <UiDropdown label="AAA" value="A" :options="['A', 'B', 'C', 'D', 'E', 'F']" />
    </template>
    <div class="right">
      <slot name="right" />
    </div>
  </div>
</template>

<style lang='scss'>
.tiptap-media-uploader {
  position: relative;
}

.tiptap-menu {
  display: flex;
  align-items: center;

  &> :last-child {
    margin-left: auto;
  }

  &>button {
    padding-inline: 0;
  }
}
</style>
