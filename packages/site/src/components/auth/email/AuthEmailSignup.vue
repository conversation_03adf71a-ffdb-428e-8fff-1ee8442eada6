<script setup>
const { sendSignInLinkToEmail } = useAuth()
const router = useRouter()

const processing = ref(false)
const message = ref('')
const form = reactive({ email: '' })

const sendRequest = async () => {
  message.value = ''
  processing.value = true
  try {
    await sendSignInLinkToEmail({
      email: form.email,
      url: `${window.location.origin}/signin`,
    })
    window.localStorage.setItem('signin-email', form.email)
    router.push({ name: 'signin', query: { email: form.email } })
  }
  catch (e) {
    message.value = e.message
    processing.value = false
  }
}
</script>

<template>
  <div>
    <h3>Log in or sign up</h3>
    <div>
      <p class="text-balance">
        We'll send you a one-time verification code. No need to remember a password. Easy.
      </p>
      <form v-if="!processing" novalidate="true" @submit.prevent="sendRequest">
        <InputBox
          v-model="form.email"
          class="centered"
          placeholder="Email address"
          label="Email address"
          type="email"
          name="email"
          autocomplete="email"
          no-spellcheck
          autofocus
          required
        />
        <div v-if="message">
          {{ message }}
        </div>
      </form>
      <div v-if="processing">
        <SpinnerIcon />
      </div>
      <button v-if="!processing" class="solid" @click="sendRequest">
        <span>Email Me Verification Code</span>
      </button>
    </div>
    <p>
      By using our service you accept our <NuxtLink to="/terms">
        terms & conditions
      </NuxtLink>
    </p>
  </div>
</template>
