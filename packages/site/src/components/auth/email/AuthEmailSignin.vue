<script setup lang="ts">
const emit = defineEmits(['ready'])

const { signInWithEmailLink } = useAuth()
const router = useRouter()
const query = useRoute().query

const showTicket = ref(false)
const showEmail = ref(false)
const processing = ref(false)
const message = ref('')

const toValue = (value: string | null | (string | null)[]): string | undefined => Array.isArray(value) ? value[0] ?? undefined : value ?? undefined

const form = reactive<{ email?: string, ticket?: string }>({})

onMounted(async () => {
  const ticket = toValue(query.ticket)
  const email = toValue(query.email)
  if (email && ticket) {
    try {
      await signIn({ email, ticket })
    }
    catch (e: any) {
      message.value = e.message
      showTicket.value = !ticket
      showEmail.value = !email
      emit('ready')
    }
  }
  else {
    showTicket.value = !ticket
    showEmail.value = !email
    emit('ready')
  }
})

const sendRequest = async () => {
  const ticket = toValue(query.ticket) || form.ticket
  const email = toValue(query.email) || form.email
  if (ticket && email) {
    message.value = ''
    processing.value = true
    try {
      await signIn({ email, ticket })
    }
    catch (e: any) {
      message.value = e.message
    }
    processing.value = false
  }
}

const signIn = async (body: { email: string, ticket: string }) => {
  await signInWithEmailLink(body)
  const account = useAuth().account.value
  await router.push(account ? getAccountRoute(account) : '/')
}
</script>

<template>
  <div>
    <form v-if="!processing" novalidate="true" @submit.prevent="sendRequest">
      <template v-if="showEmail">
        <h4>Please provide your email for confirmation</h4>
        <input
          v-model="form.email"
          placeholder="Email"
          type="email"
          name="email"
          autocomplete="email"
          autofocus
          required>
      </template>
      <template v-if="showTicket">
        <h4>Enter the verification code sent to your email</h4>
        <InputBox
          v-model="form.ticket"
          placeholder="1234567"
          class="code lg centered"
          type="text"
          name="ticket"
          autocomplete="off"
          no-spellcheck
          autofocus
          required
        />
      </template>
      <div v-if="message">
        {{ message }}
      </div>
    </form>
    <div v-if="processing">
      <SpinnerIcon />
    </div>
    <footer>
      <button class="solid" @click="sendRequest">
        Sign In
      </button>
    </footer>
  </div>
</template>

<style lang="scss">
.code input {
  width: 9ch;

  &::placeholder {
    opacity: 0.2;
  }
}
</style>
