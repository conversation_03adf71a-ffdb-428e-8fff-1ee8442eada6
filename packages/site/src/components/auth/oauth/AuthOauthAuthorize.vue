<script setup lang="ts">
interface Client {
  name: string
}

const { logout, account, oauthAuthorize } = useAuth()
const router = useRouter()
const query = useRoute().query

const { data: client, error } = await useFetch<Client>(`/api/client/${query.client_id}`)
if (error.value?.statusCode === 401) {
  account.value = undefined
  router.push('/')
}

const onLogout = async () => {
  await logout()
  router.push('/')
}

const onAuthorize = async () => {
  const { redirectUri } = await oauthAuthorize({
    redirectUri: query.redirect_uri as string,
    responseType: query.response_type as string,
    clientId: query.client_id as string,
    state: query.state as string,
  })
  window.location.href = redirectUri
  // If href contains a hash, the browser does not reload the page. We reload manually.
  if (redirectUri.includes('#')) {
    window.location.reload()
  }
}
</script>

<template>
  <div>
    <div>
      <div>
        <p>Signed in as:</p>
        <p>[email]</p>
      </div>
      <button aria-label="Change Account" @click="onLogout">
        logout
      </button>
    </div>
    <h2>Authorization required</h2>
    <p>
      <strong>{{ client?.name }}</strong> <span>would like permission to access your account. It is a third-party
        application.</span>
      <strong>If you do not trust it, then you should not authorize it.</strong>
    </p>
    <h2>Review permissions</h2>
    <div>
      <strong>Everything</strong>
      <span>Read and write access</span>
    </div>
    <button @click="onAuthorize">
      Authorize
    </button>
  </div>
</template>
