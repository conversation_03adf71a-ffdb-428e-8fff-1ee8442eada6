<script setup lang="ts">
import type { mastodon } from '#shared/types'

withDefaults(defineProps<{
  account: mastodon.v1.Account
  sizes?: string
}>(), {
  sizes: '50px',
})
</script>

<template>
  <UiImg v-if="account?.avatar" class="account-avatar" :src="account.avatar" :sizes="sizes" square />
</template>

<style lang="scss">
.account-avatar {
  width: var(--badge-size);
  height: var(--badge-size);
  border-radius: calc(var(--badge-size) * var(--border-radius-fact, 0));
  margin: 0;
}
</style>
