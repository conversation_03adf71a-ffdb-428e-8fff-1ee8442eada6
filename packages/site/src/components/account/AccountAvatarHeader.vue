<script setup lang="ts">
import type { mastodon } from '#shared/types'

const props = defineProps<{
  account: mastodon.v1.Account
}>()

const isSelf = useSelfAccount(() => props.account)
const updatedAccount = ref()
const editor = ref()
const account = computed({
  get: () => updatedAccount.value ?? props.account,
  set: (newVal) => { updatedAccount.value = newVal },
})
function onSave(newAccount: mastodon.v1.Account) {
  account.value = newAccount
  editor.value?.close()
}
</script>

<template>
  <UiPopup
    v-if="isSelf"
    ref="editor"
    class="account-avatar-header"
    theme="indi-avatar">
    <template #trigger-title>
      <AccountAvatar :account="account" />
    </template>
    <AccountAvatarEditor
      :account="account"
      :image="account.avatar"
      @saved="onSave"
    />
  </UiPopup>
  <AccountAvatar v-else :account="account" />
</template>
