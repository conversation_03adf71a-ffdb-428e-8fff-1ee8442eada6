<script setup lang="ts">
import type { mastodon } from '#shared/types'

const props = defineProps<{
  account: mastodon.v1.Account
}>()

const shouldOpenEditor = ref(false)
const isSelf = useSelfAccount(() => props.account)
const isLoggedIn = useAuth().loggedIn
const themeEditor = ref()
const themeEditorPopup = ref()

function handleClose() {
  shouldOpenEditor.value = false
}

function saveTheme() {
  themeEditor.value?.saveTheme()
  themeEditorPopup.value?.close()
}
function onThemeEditorChanged({ isVisible }: { isVisible: boolean }) {
  if (isVisible) {
    themeEditor.value?.cacheOriginalTheme()
  }
  else {
    themeEditor.value?.cancel()
  }
}
</script>

<template>
  <HeaderMain>
    <template #header-start>
      <UiBar
        padding
        bg
        corner
        class="profile-badge">
        <AccountAvatarHeader :account="account" />
        <div>
          <h1>
            <AccountDisplayName :account="account" />
          </h1>
          <UiResponsiveMenu>
            <AccountStats :account="account" />
          </UiResponsiveMenu>
        </div>
      </UiBar>
    </template>
    <template #header-end>
      <UiBar v-if="isLoggedIn" padding bg class="account-tools">
        <template v-if="isSelf">
          <button class="compact" @click="shouldOpenEditor = true">
            <span class="icon-pencil" />
          </button>
          <UiPopup ref="themeEditorPopup" @state="onThemeEditorChanged">
            <template #trigger-title>
              <ThemeEditorButton ref="themeButton" class="theme-button" />
            </template>
            <template #default="{ close }">
              <ThemeEditor ref="themeEditor" @on-save="saveTheme" @close-request="close" />
            </template>
          </UiPopup>
        </template>
        <template v-else>
          <AccountFollowButton :account="account" />
          <AccountMessageButton :account="account" />
        </template>
        <UiPopoverModal v-if="shouldOpenEditor" @request-close="handleClose">
          <div class="content-box-layout">
            <div class="content-box">
              <PublishWidget draft-key="home" class="account-theme" @close="handleClose" />
            </div>
          </div>
        </UiPopoverModal>
      </UiBar>
    </template>
  </HeaderMain>
</template>

<style lang="scss" scoped>
.profile-badge {
  // --button-height: var(--base-size);
  --badge-size: calc(var(--button-height) + var(--padding-mini));
  display: flex;
  align-items: center;
  gap: var(--padding-small);
  width: fit-content;
  padding-inline-start: var(--padding-small);
  border-start-start-radius: 0;
  // border-start-end-radius: 0;
  border-end-start-radius: 0;
  .account-stats {
    margin-bottom: calc(var(--padding-mini) * -1);
  }
}

.account-tools {
  margin-inline: auto var(--padding-base);
}

h1 {
  margin: 0;
  font-size: calc(var(--h4) * 0.85);
  color: var(--color-accent);
}
</style>
