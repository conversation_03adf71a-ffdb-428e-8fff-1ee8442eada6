<script setup lang="ts">
import type { mastodon } from '#shared/types'

defineProps<{
  account: mastodon.v1.Account
}>()
</script>

<!-- TODO: reuse AccountInfo.vue -->

<template>
  <AccountBadge :account="account" class="account-mini-badge">
    <template #meta>
      <AccountStats :account="account" mini />
    </template>
  </AccountBadge>
</template>

<style>
.account-mini-badge {
  .account-info {
    .account-name {
      font-size: var(--font-size-body);
    }
    .desc {
      font-size: var(--font-size-micro);
      .account-stats {
        height: auto;
      }
    }
  }
}
</style>
