<script setup lang="ts">
const input = ref('')
const answer = ref('')

let abortController: AbortController | null = null

async function triggerRequest(prompt: string) {
  try {
    abortController = new AbortController()

    const res = await fetch('/api/ai/question', {
      method: 'POST',
      body: JSON.stringify({
        prompt,
        stream: true,
      }),
      headers: {
        'Accept': 'text/event-stream',
        'Content-Type': 'application/json',
      },
      signal: abortController.signal,
    }).catch((err) => {
      throw err
    })

    if (!res.ok) {
      throw new Error((await res.text()) || 'Failed to fetch the chat response.')
    }
    if (!res.body) {
      throw new Error('The response body is empty.')
    }

    const reader = res.body.getReader()
    const decoder = new TextDecoder()

    while (true) {
      const { done, value } = await reader.read()
      if (done) {
        break
      }
      if (value) {
        // Update the chat state with the new message tokens.
        answer.value += decoder.decode(value, { stream: true })
      }

      // The request has been aborted, stop reading the stream.
      if (abortController === null) {
        reader.cancel()
        break
      }
    }

    abortController = null
  }
  catch (err) {
    // Ignore abort errors as they are expected.
    if ((err as any).name === 'AbortError') {
      abortController = null
      return null
    }
  }
}

const handleSubmit = () => {
  const inputValue = input.value
  if (!inputValue) {
    return
  }
  return triggerRequest(inputValue)
}
</script>

<template>
  <input v-model="input" type="input">
  <input type="button" @click="handleSubmit">
  <div>{{ answer }}</div>
</template>
