<script setup lang="ts">
const props = withDefaults(defineProps<{
  modelValue: any
  min?: number | string
  step?: number | string
  max?: number | string
  title?: string
}>(), {
  min: 0,
  step: 1,
  max: 100,
  title: 'Range input',
})
const emit = defineEmits(['update:modelValue'])
const model = computed({
  get: () => props.modelValue,
  set: (newVal) => { emit('update:modelValue', newVal) },
})
</script>

<template>
  <div class="input-range">
    <div class="bg">
      <input
        v-model="model"
        type="range"
        :min="props.min"
        :step="props.step"
        :max="props.max"
        :title="props.title">
    </div>
  </div>
</template>

<style lang='scss' scoped>
.input-range {
  --track-h: var(--size-two-third, 2rem);
  --track-border-radius: calc(var(--track-h) * 0.5);
  --track-bg: #ccc;
  --thumb-border-w: 2px;
  --thumb-color: white;
  --thumb-border-color: #909090;

  position: relative;
  height: var(--track-h);
  align-items: center;
  padding: 0;

  &:not(:last-child) {
    margin-bottom: var(--padding-base);
  }

  .bg {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: var(--track-border-radius);
    background: var(--track-bg);
  }
}
@mixin slider-track {
  position: relative;
  width: 100%;
  height: var(--track-h);
  background: transparent;
  border-radius: var(--track-border-radius);
  box-sizing: border-box;
}

@mixin slider-thumb {
  --thumb-size: var(--track-h);
  position: relative;
  width: var(--thumb-size);
  height: var(--thumb-size);
  background: var(--thumb-color);
  border: solid var(--thumb-border-w) var(--thumb-border-color);
  border-radius: var(--track-border-radius);
  // border-width: 0;
  cursor: pointer;
  transition: border 0.4s;
}

input[type=range] {
  -webkit-appearance: none;
  /* Hides the slider so that custom slider can be made */
  width: 100%;
  /* Specific width is required for Firefox. */
  height: 100%;
  /* Specific width is required for Firefox. */
  background: transparent;
  /* Otherwise white in Chrome */
  min-height: unset;
  display: flex;
  padding: 0;

  &:focus {
    // outline: none; /* Removes the blue border. You should probably do some kind of focus styling for accessibility reasons though. */
    // box-shadow: none;
    // border: none;
  }

  &::-moz-focus-inner {
    outline: none;
    border: none;
  }

  &::-ms-track {
    width: 100%;
    cursor: pointer;
    /* Hides the slider so custom styles can be added */
    background: transparent;
    border-color: transparent;
    color: transparent;
  }

  &::-webkit-slider-runnable-track,
  &::-moz-range-track,
  &::-ms-track {
    @include slider-track;
  }

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    @include slider-thumb;
  }

  &::-moz-range-thumb,
  &::-ms-thumb {
    @include slider-thumb;
  }

  // &::-webkit-slider-thumb:focus,
  // &::-moz-range-thumb:focus {
  //   // box-shadow: 0 0 0 5px red;
  //   // border-width: 5px;
  // }
}
</style>
