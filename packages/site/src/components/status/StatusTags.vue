<script setup lang="ts">
import type { mastodon } from '#shared/types'

defineProps<{
  status: mastodon.v1.Status
}>()
</script>

<template>
  <ul v-if="status.tags?.length" class="tags">
    <li v-for="tag in status.tags" :key="tag.id">
      <NuxtLink :to="{ name: 'tags-id', params: { id: tag.id } }">
        {{ tag.name }}
      </NuxtLink>
    </li>
  </ul>
</template>

<style>
.tags {
  position: relative;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  gap: var(--padding-small);
  padding-top: var(--base-size);
  font-size: var(--font-size-sm);

  li {
    margin: 0;
    background-color: color-mix(in lab,
        rgb(180 180 180 / 50%) 70%,
        color-mix(in lab,
          var(--color-bg) 20%,
          var(--color-panel)));
    padding-inline: var(--padding-small);
    border-radius: calc(var(--corner-radius) * 0.75);

    a {
      text-decoration: none;

      &:hover {
        color: var(--color-text);
      }
    }
  }
}
</style>
