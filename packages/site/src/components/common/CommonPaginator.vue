<script setup lang="ts" generic="T, E">
withDefaults(defineProps<{
  items: T[]
  keyProp: keyof T
  pending?: boolean
  error?: E | null
  endMessage?: boolean | string
}>(), {
  pending: false,
  error: null,
  endMessage: true,
})

defineSlots<{
  default: (props: {
    items: T[]
    item: T
    index: number
    active?: boolean
    older: T
    newer: T // newer is undefined when index === 0
  }) => void
  items: (props: {
    items: T[]
  }) => void
  loading: () => void
  done: () => void
}>()
</script>

<template>
  <div class="paginator">
    <slot name="items" :items="items">
      <slot
        v-for="item, index of items"
        v-bind="{ key: item[keyProp as keyof T] }"
        :item="item as T"
        :older="items[index + 1] as T"
        :newer="items[index - 1] as T"
        :index="index"
        :items="items as T[]"
      />
    </slot>
    <slot v-if="pending" name="loading">
      <div>loading</div>
    </slot>
    <slot v-else-if="error === null && endMessage !== false" name="done" />
    <div v-else-if="error">
      {{ error }}
    </div>
  </div>
</template>
