<script setup lang="ts">
withDefaults(defineProps<{ message?: string }>(), { message: '' })
</script>

<template>
  <div class="ui-processing-overlay">
    <SpinnerIcon :class="{ 'with-message': !!message }">
      <span v-if="message">{{ message }}</span>
    </SpinnerIcon>
  </div>
</template>

<style lang='scss'>
.ui-processing-overlay{
  position: absolute;
  inset: 0;
  background-color: rgb(255 255 255 / 0.4);
  backdrop-filter: blur(5px);
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
