<script setup lang="ts">
import type { Placement } from 'tippy.js'
import { Tippy } from 'vue-tippy'

withDefaults(defineProps<{
  placement?: Placement
}>(), {
  placement: 'bottom-start',
})

const emit = defineEmits(['on-keydown', 'state'])
const tippy = ref<typeof Tippy>()
const dropdownButton = ref<HTMLElement>()

function getBody() {
  return document?.body
}
const durationMs = ref(100)

function focusButton() {
  dropdownButton.value?.focus()
}
const show = computed(() => tippy.value?.show)
defineExpose({ show })
</script>

<template>
  <Tippy
    v-if="isHydrated"
    ref="tippy"
    interactive
    allow-html
    theme="indi"
    :arrow="false"
    :hide-on-click="true"
    :append-to="getBody"
    :offset="[0, 8]"
    :duration="durationMs"
    trigger="click"
    :placement="placement"
    :on-hide="focusButton"
    @state="emit('state', $event)">
    <template #default="{ state }">
      <button
        ref="dropdownButton"
        class="dropdown-button"
        @keydown="emit('on-keydown', $event)">
        <slot name="title">
          Dropdown
        </slot>
        <span v-if="!state.isVisible" class="icon-menu-down" />
        <span v-else class="icon-menu-up" />
      </button>
    </template>

    <template #content="{ state, hide }">
      <UiCollapsibleBox :open="state.isVisible" :t="`${durationMs}ms`">
        <slot :close="hide" />
      </UiCollapsibleBox>
    </template>
  </Tippy>
</template>

<style lang="scss" scoped>
.dropdown-button {
  padding-right: var(--padding-mini);
}
.tippy-content {
  padding: 0;
}
// .tippy-box[data-animation=fade][data-state=hidden]{opacity:0}
// [data-tippy-root]{max-width:calc(100vw - 10px)}
// .tippy-box{ position:relative;background-color:#333;color:#fff;border-radius:4px;font-size:14px;line-height:1.4;white-space:normal;outline:0;transition-property:transform,visibility,opacity}.tippy-box[data-placement^=top]>.tippy-arrow{bottom:0}.tippy-box[data-placement^=top]>.tippy-arrow:before{bottom:-7px;left:0;border-width:8px 8px 0;border-top-color:initial;transform-origin:center top}.tippy-box[data-placement^=bottom]>.tippy-arrow{top:0}.tippy-box[data-placement^=bottom]>.tippy-arrow:before{top:-7px;left:0;border-width:0 8px 8px;border-bottom-color:initial;transform-origin:center bottom}.tippy-box[data-placement^=left]>.tippy-arrow{right:0}.tippy-box[data-placement^=left]>.tippy-arrow:before{border-width:8px 0 8px 8px;border-left-color:initial;right:-7px;transform-origin:center left}.tippy-box[data-placement^=right]>.tippy-arrow{left:0}.tippy-box[data-placement^=right]>.tippy-arrow:before{left:-7px;border-width:8px 8px 8px 0;border-right-color:initial;transform-origin:center right}.tippy-box[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.54,1.5,.38,1.11)}.tippy-arrow{width:16px;height:16px;color:#333}.tippy-arrow:before{content:"";position:absolute;border-color:transparent;border-style:solid}.tippy-content{position:relative;padding:5px 9px;z-index:1}
</style>
