<script setup lang="ts">
// export type ResponsiveMenuWidth = 'xs' | 's' | 'm' | 'l' | 'xl'
// defineProps<{
//   maxWidth?: ResponsiveMenuWidth
// }>()
</script>

<template>
  <UiPopup class="ui-responsive-menu">
    <template #trigger-title>
      <slot name="trigger-title">
        <span class="icon-more horizontal" />
      </slot>
    </template>
    <span class="ui-responsive-menu-content">
      <slot />
    </span>
  </UiPopup>
  <div class="ui-responsive-menu-wide">
    <slot />
  </div>
</template>

<style>
.ui-responsive-menu {
  position: relative;
  button {
    --icon-size: 1.3rem;
    min-height: 0;
    height: auto;
    width: calc(var(--base-size) * 2);
    justify-content: flex-start;
  }
  @container (width > 1000px) {
    display: none;
  }
}
.ui-responsive-menu-wide {
  @container (width <= 1000px) {
    display: none;
  }
}
.icon-more.horizontal {
  rotate: 90deg;
}
</style>
