.theme-bg {
  background-color: var(--color-bg-rgb);
  background-color: var(--color-bg-p3);
}
.theme-panel-bg {
  background-color: var(--color-panel-rgb);
  background-color: var(--color-panel-p3);
}
.theme-text {
  color: var(--color-text);
  // color: var(--color-text-p3);
}
.theme-text-accent {
  color: var(--color-accent-rgb);
  color: var(--color-accent-p3);
}
.theme-container-grid {
  --scale-fact: 0.3;
  --cell-w: 23cqi;
  --gap: max(var(--grid-gap) * var(--scale-fact), 2px);;
  container-type: inline-size;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(23cqi, 1fr));
  // grid-template-columns: repeat(4, 1fr);
  grid-auto-rows: 23cqi;
  grid-auto-flow: dense;
  gap: var(--gap);
  padding: var(--gap);
  transition: gap 0.2s;

  .cell {
    &.horizontal {
      @container (width > 50px) {
        grid-column: auto / span 2;
      }
    }

    &.vertical {
      grid-row: span 2;
    }

    &.large,
    &.big {
      grid-row: span 2;
      @container (width > 50px) {
        grid-column: span 2;
      }
    }
  }
}