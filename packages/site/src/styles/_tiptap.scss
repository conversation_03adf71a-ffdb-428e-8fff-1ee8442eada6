.ProseMirror {
  p.is-empty::before {
  /* p[data-placeholder]:not([data-placeholder=""])::before { */
    content: attr(data-placeholder);
    float: left;
    pointer-events: none;
    height: 0;
    opacity: 0.4;
  }
  span[data-type='mention'],
  span[data-type='hashtag'] {
    --at-apply: text-primary;
  }
}

ul[data-type="poll"] {
  list-style: none;
  padding: var(--padding-base);
  background-color: var(--color-input-bg);
  border-radius: var(--border-radius-small);

  li {
    display: flex;
    align-items: center;
    gap: var(--padding-small);
    margin-left: 0;
  }
}