:root {
  --dropdown-max-h: calc(100svh - var(--base-size) - var(--padding-base));
}
.tippy-box[data-theme^='indi'],
.tippy-box[data-theme~='indi-light'],
.tippy-box[data-theme~='indi-avatar'] {
  --bg: var(--color-panel-minor);
  --border-radius: var(--corner-radius, var(--border-radius-medium));
  position: relative;
  background-color: var(--bg);
  color: var(--color-panel-text);
  border-radius: var(--border-radius);
  white-space: normal;
  transition-property: transform, visibility, opacity;
  padding: 0;
  
  .tippy-content {
    border-radius: var(--border-radius);
    padding: 0;
    overflow: clip;

    button {
      color: inherit;
    }
  }

  &[data-placement^='top'] > .tippy-arrow::before {
    border-top-color: var(--bg);
  }
  &[data-placement^='bottom'] >.tippy-arrow::before {
     border-bottom-color: var(--bg);
   }
  &[data-placement^='left'] >.tippy-arrow::before {
     border-left-color: var(--bg);
   }
  &[data-placement^='right'] >.tippy-arrow::before {
     border-right-color: var(--bg);
   }
}
.tippy-box[data-theme~='indi-light'] {
  --bg: white;
}
.tippy-box {
  max-height: var(--dropdown-max-h);
  overflow: auto;
  overscroll-behavior: contain;
  box-shadow: var(--grid-active-shadow);
}
.tippy-box[data-theme~='indi-avatar'] {
  --box-w: min(320px, calc(100vw - var(--padding-base)));
  --border-radius: calc(var(--box-w) * var(--border-radius-fact));
  width: var(--box-w);
  aspect-ratio: 1 / 1;
}
.tippy-box[data-theme~='indi-button-group'] {
  --border-radius: var(--border-radius-base);
}
.tippy-box[data-theme~='indi-clear'] {
  --bg: transparent;
}
