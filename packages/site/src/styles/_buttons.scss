@use "mixins";
:root {
  --button-border-radius: calc(var(--button-height) * var(--button-radius-fact));
  --button-border-w: 0;
  --button-border-style: solid;
  --button-border-color: transparent;
  --button-font-size: var(--font-size-body, 100%);
  --button-padding-inline: var(--padding-base);

  // --button-text-color: var(--color-accent, #0060ff);
  --button-bg-color: var(--color-accent-rgb);
  --color-button-bg-focus: var(--theme-color-button-bg-focus, #ccc);
  --color-button-bg-hover: var(--theme-color-button-bg-hover, var(--color-button-bg-focus));
  --color-button-bg-active: var(--theme-color-button-bg-active, var(--color-button-bg-focus));
}

button,
.button,
.btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--button-height);
  min-width: var(--button-height);
  background-color: transparent;
  border-radius: var(--border-radius-base);
  border-width: var(--button-border-w, 0);
  border-style: var(--button-border-style, solid);
  border-color: var(--button-border-color, var(--color-line-light, rgba(0 0 0 / 0.15)));
  font-size: var(--button-font-size, var(--font-size-body));

  color: var(--color-text);
  cursor: pointer;
  padding: 0 var(--button-padding-inline);
  white-space: nowrap;
  @include mixins.not-selectable();

  &.solid {
    background-color: var(--color-accent-rgb);
    color: var(--color-bg);
  }

  &:active,
  &:hover,
  &:focus,
  &.focused {
    position: relative;

    &:after {
      content: '';
      display: block;
      position: absolute;
      width: 100%;
      height: 100%;
      @include mixins.centered;
      background: var(--color-bg);
      border-radius: inherit;
      opacity: 0.4;
      z-index: -1;
    }
  }

  &:active {
    &:after {
      background: rgba(0, 0, 0, 0.1);
    }
  }

  &:disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  &.compact {
    padding: 0;
  }

  &.large {
    --button-height: var(--base-size, 3rem);
  }

  &.small {
    --button-height: 2rem;
    padding: 0;
  }

  &.square {
    width: var(--button-height);
  }

  &.round {
    border-radius: 999px;
  }
}

.button-group {
  display: flex;
  align-items: center;

  &>button,
  &>.button,
  &>.btn {
    border-radius: 0;

    &:last-child {
      border-top-right-radius: var(--button-border-radius);
      border-bottom-right-radius: var(--button-border-radius);
    }

    &:first-child {
      border-top-left-radius: var(--button-border-radius);
      border-bottom-left-radius: var(--button-border-radius);
    }
  }
}