@use "mixins";
.select-none { user-select: none; }

.cursor-pointer { cursor: pointer; }

.no-border { border: none; }

.transparent { background: transparent; }

.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }


.overlay-right-top {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
}

.bg-white {
  background: white;
}

.opacity-1 { opacity: 1; }
.opacity-0 { opacity: 0; transition: opacity var(--transition-time, 0.2s) }

.ml-auto { margin-left: auto; }
.mr-auto { margin-right: auto; }

.color-label { color: var(--color-label, #878787); }

.flex-wrap {
  display: flex;
  flex-flow: wrap;
}

.flex-row-center {
  display: flex;
  align-items: center;
}
.flex-center-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-row-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-column {
  display: flex;
  flex-direction: column;
  &-center {
    align-items: center;
  }
}

.z-top {
  position: relative;
  z-index: 20000;
}

.text-block {
  max-width: var(--max-width-text-block, 55ch);
  font-family: 'Roboto', sans-serif;
}

.trimmed-text {
  &.two-rows {
    @include mixins.multi-line-truncate(2)
  }
  &.three-rows {
    @include mixins.multi-line-truncate(3)
  }
  &.four-rows {
    @include mixins.multi-line-truncate(4)
  }
}

.truncate {
  --n: var(--truncate-lines, 2);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: var(--n);
  overflow: hidden;
}

.inline-block {
  display: inline-block;
}

.center-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-ui {
  --ui-color-bg: var(--color-panel);
  // background-color: var(--color-panel-ui);
  // backdrop-filter: blur(var(--ui-filter-blur, 25px));
  // color: var(--color-accent);
}

.bg-ui {
  --ui-color-bg: var(--color-bg);
  // background-color: var(--color-bg-ui);
  // backdrop-filter: blur(var(--ui-filter-blur, 25px));
  // color: var(--color-text);
}

