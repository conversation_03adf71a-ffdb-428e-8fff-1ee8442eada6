// @use "vars";
@use "mixins";
// @import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,300;0,400;0,500;1,300;1,400;1,500&display=swap');
// @import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;1,300;1,400;1,500&display=swap');
// @import url('https://fonts.googleapis.com/css2?family=Wix+Madefor+Text:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&family=Red+Hat+Mono:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&family=Wix+Madefor+Text:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap');

:root {
  --z-index-modal: 2000;
  --z-index-float: 2500;
  --z-index-overlays: 3000;
}

html * {
  box-sizing: border-box;
}

html,
body,
#__nuxt,
#__layout {
  font-family: var(--font-family-system);
  line-height: 1.4;
}

.content,
.text-block {
  letter-spacing: 0.02em;
}

body {
  width: 100vw;
  color: var(--color-text);
}

h1,
h2,
h3,
h4 {
  margin-bottom: 1em;
  text-wrap: balance;
  line-height: 1em;
}

h1 {
  font-size: var(--h1, 1.5rem);
  font-weight: 400;
  color: var(--color-text-title);
  letter-spacing: -0.2px;
}

h2 {
  font-size: var(--h2, 1.25rem);
  color: var(--color-text-title);
}

h3 {
  line-height: 1.1em;
  font-size: var(--h3, 1.125rem);
}

input,
textarea,
button,
a {
  &:focus {
    outline: none;
  }
}

input::placeholder,
textarea::placeholder,
.placeholder {
  color: var(--placeholder-color, color-mix(in lab, var(--color-text, rgba(0, 0, 0)), transparent 85%));
}

a {
  color: var(--color-accent);

  &:focus,
  &:hover {
    text-decoration: underline;
  }
}

a.no-decoration {
  text-decoration: none;
  &:focus,
  &:hover {
    text-decoration: none;
  }
}

p {
  margin-bottom: 1em;
  text-wrap: balance;
}

em {
  font-style: italic;
}

strong {
  font-weight: bold;
}

strong em {
  font-weight: bold;
}

.text-balance {
  text-wrap: balance;
}

.desc {
  font-size: var(--font-size-caption, 0.8rem);
}

.error {
  color: var(--color-error);
}

.semi-trans {
  @include mixins.semi-trans;

  &.semi-trans-to-bottom {
    overflow: clip;

    &:before {
      content: '';
      position: absolute;
      display: block;
      height: 100%;
      width: 100%;
      top: 0;
      left: 0;
      z-index: -5;
      background: linear-gradient(180deg, white, transparent);
    }
  }
}

.semi-trans-strong {
  @include mixins.semi-trans($blur: 30px);
}

.semi-trans-panel {
  background-color: var(--color-bg-semitransparent-panel, rgba(245, 245, 245, 0.8));
  backdrop-filter: blur(15px);
}

button.semi-trans {
  background-color: var(--color-bg-semitransparent-panel, rgba(255, 255, 255, 0.3));
  backdrop-filter: blur(15px);

  &:hover {
    background: var(--color-bg-semitransparent, rgba(255, 255, 255, 0.8));
  }
}

label {
  &.disabled {
    opacity: 0.3;
    pointer-events: none;
  }
}

.tags-line {
  display: flex;
  flex-flow: wrap;
  align-items: center;

  li {
    padding: var(--padding-mini) var(--padding-small);
    background: var(--color-line-light);
    font-size: var(--font-size-caption);
    margin: 0 var(--padding-mini) var(--padding-mini) 0;
  }
}

.spin {
  animation: spin 0.5s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.translation-auto {
  --color-input-bg: #f8fbeb;
}

.slideshow-embedded footer .expandable-caption {
  position: sticky;
  bottom: 0;
  padding: var(--padding-small) var(--padding-base) var(--padding-base);
  background-color: lch(var(--theme-l) var(--theme-c) var(--theme-h) / 0.85);
  backdrop-filter: blur(15px);

  &::after {
    content: none;
  }
}
