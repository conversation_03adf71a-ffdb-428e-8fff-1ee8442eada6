<script setup lang="ts">
import type { mastodon } from '#shared/types'

definePageMeta({
  layout: 'empty',
})

const { data, status, refresh } = useFetch<mastodon.v1.Admin.Account[]>('/api/admin/accounts')
const pendingDelete = ref('')

async function deleteAccount(acct: string) {
  pendingDelete.value = acct
  await $fetch(`/api/admin/accounts/${acct}`, { method: 'DELETE' })
  await refresh()
  pendingDelete.value = ''
}
</script>

<template>
  <SpinnerIcon v-if="status === 'pending'" />
  <ul v-else-if="data">
    <li v-for="{ id, account } in data" :key="id">
      <NuxtLink :to="getAccountRoute(account)">
        <div>
          <header>
            <AccountAvatar :account="account" sizes="96" />
          </header>
          <div class="title">
            <AccountDisplayName :account="account" />
          </div>
        </div>
      </NuxtLink>
      <button type="button" class="compact" :disabled="pendingDelete === account.acct" @click="deleteAccount(account.acct)">
        <SpinnerIcon v-if="pendingDelete === account.acct" /> <span class="icon-trash" />
      </button>
    </li>
  </ul>
  <div v-else>
    &nbsp;
  </div>
</template>
