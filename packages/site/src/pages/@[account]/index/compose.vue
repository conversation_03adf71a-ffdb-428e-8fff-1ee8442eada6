<script setup lang="ts">
defineOptions({
  name: 'Compose',
})
definePageMeta({
  name: 'compose',
  middleware: 'auth',
})
const { account: acct } = useRoute().params
const router = useRouter()
const account = await fetchAccountByHandle(`${acct}`)
const theme = useTheme(computed(() => account?.theme))

function handleClose() {
  router.push({ name: 'account-index', params: { account: acct } })
}
</script>

<template>
  <UiPopoverModal @request-close="handleClose">
    <PublishWidget class="account-theme theme" :style="theme" @close="handleClose" />
  </UiPopoverModal>
</template>
