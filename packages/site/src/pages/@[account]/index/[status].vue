<script setup lang="ts">
defineOptions({
  name: 'Status',
})
definePageMeta({
  name: 'status',
  key: route => route.path,
})

const { status: id, account: acct } = useRoute().params
const router = useRouter()

const { data: status, pending, refresh: refreshStatus } = await useAsyncData(
  `status:${id}`,
  () => fetchStatus(`${id}`),
)

onReactivated(() => {
  refreshStatus()
})

function close() {
  router.push({ name: 'account-index', params: { account: acct } })
}
</script>

<template>
  <SpinnerIcon v-if="pending" />
  <StatusPopover v-else :status="status" :close-func="close" />
</template>
