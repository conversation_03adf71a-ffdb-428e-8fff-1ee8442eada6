<script setup lang="ts">
defineOptions({
  name: 'AccountIndex',
})
definePageMeta({
  name: 'account-index',
  key: route => `${useRuntimeConfig().public.domain}:${route.params.account}`,
  layout: 'default',
})

const { account: id } = useRoute().params
const { account: person } = useAuth()
const { data, status, refresh } = await useAsyncData(`account:${id}`, () => {
  const accountId = person.value?.id
  if (accountId && compareHandle(`${id}`, accountId)) {
    return Promise.resolve(person.value)
  }
  return fetchAccountByHandle(`${id}`)
})
const account = computed(() => {
  const accountId = person.value?.id
  if (accountId && compareHandle(`${id}`, accountId)) {
    return person.value
  }
  return data.value
})
const theme = useTheme(computed(() => account.value?.theme))

const url = `/api/accounts/${id}/statuses`
const channel = `timeline:account:${toHandle({ username: `${id}`, domain: useRuntimeConfig().public.domain })}`

onReactivated(() => {
  refresh()
})
</script>

<template>
  <div class="account-area theme" :style="theme">
    <template v-if="status === 'pending'" />
    <template v-else-if="account">
      <AccountHeader :account="account" />
      <TimelineStatuses :url="url" :channel="channel" />
      <NuxtPage />
    </template>
    <CommonNotFound v-else>
      {{ `not_found @${id}` }}
    </CommonNotFound>
  </div>
</template>

<style lang="scss">
:root {
  background-color: var(--color-bg-rgb);

  @media (color-gamut: p3) {
    background-color: var(--color-bg-p3);
  }
}

.account-area {
  background-color: var(--color-bg);
  color: var(--color-text);
  container-type: inline-size;
  position: relative;
  min-height: 100vh;
  margin-inline: auto;
}
</style>
