interface PartSource {
  partNumber: number
  start: number
  end: number
}

interface CreateOutput {
  uploadUrl: string
  authorizationToken: string
  fileId: string
  parts: PartSource[]
}

interface UploadedPart {
  partNumber: number
  sha1: string
}

type ProgressFn = (payload: { sent: number, total: number, percentage: number }) => void
type ErrorFn = (error: any) => void

interface UploaderParams {
  baseURL: string
  threadsQuantity?: number
  onProgressFn?: ProgressFn
  onErrorFn?: ErrorFn
}

interface SenderParams {
  baseURL: string
  threadsQuantity?: number
  onProgressFn: ProgressFn
  onErrorFn: ErrorFn
  uploadUrl: string
  authorizationToken: string
  file: File
  fileId: string
  parts: PartSource[]
}

class Sender {
  private readonly baseURL: string
  private readonly threadsQuantity: number
  private readonly onProgressFn: ProgressFn
  private readonly onErrorFn: ErrorFn
  private readonly uploadUrl: string
  private readonly authorizationToken: string
  private readonly file: File
  private readonly fileId: string
  private readonly parts: PartSource[]

  private readonly progressCache: Record<number, number> = {}
  private readonly activeConnections: Record<number, XMLHttpRequest> = {}
  private readonly uploadedParts: UploadedPart[] = []

  // adjust the timeout value to activate exponential backoff retry strategy
  private timeout = 0
  private uploadedSize = 0

  constructor({ baseURL, threadsQuantity, onProgressFn, onErrorFn, uploadUrl, authorizationToken, file, fileId, parts }: SenderParams) {
    this.baseURL = baseURL
    this.threadsQuantity = Math.min(threadsQuantity || 5, 15)
    this.onProgressFn = onProgressFn
    this.onErrorFn = onErrorFn
    this.uploadUrl = uploadUrl
    this.authorizationToken = authorizationToken
    this.file = file
    this.fileId = fileId
    this.parts = parts
  }

  abort() {
    Object.keys(this.activeConnections).map(Number).forEach((id) => {
      this.activeConnections[id].abort()
    })
  }

  sendNext(retry = 0) {
    const countConnections = Object.keys(this.activeConnections).length
    if (countConnections >= this.threadsQuantity) {
      return
    }
    if (!this.parts.length) {
      if (!countConnections) {
        if (this.uploadedParts.length) {
          const parts = this.uploadedParts.sort((a, b) => a.partNumber - b.partNumber).map(({ sha1 }) => sha1)
          this.sendComplete(this.fileId, parts).catch((error) => {
            this.onErrorFn(error)
          })
        }
      }
      return
    }

    const part = this.parts.pop()
    if (part) {
      this.sendChunk(this.file.slice(part.start, part.end), part.partNumber, () => { this.sendNext() })
        .then(() => {
          this.sendNext()
        })
        .catch((error) => {
          if (retry <= 6) {
            retry++
            const wait = (ms: number) => new Promise(resolve => window.setTimeout(resolve, ms))
            // exponential backoff retry before giving up
            console.error(`Part#${part.partNumber} failed to upload, backing off ${2 ** retry * 100} before retrying...`)
            wait(2 ** retry * 100).then(() => {
              this.parts.push(part)
              this.sendNext(retry)
            })
          }
          else {
            console.error(`Part#${part.partNumber} failed to upload, giving up`)
            this.onErrorFn(error)
          }
        })
    }
  }

  private sendComplete(fileId: string, parts: string[]) {
    return $fetch('/finish', {
      baseURL: this.baseURL,
      method: 'POST',
      body: {
        fileId,
        parts,
      },
    })
  }

  private async sendChunk(blob: Blob, partNumber: number, sendChunkStarted: () => void) {
    const blobBuffer = await blob.arrayBuffer()
    const cryptoBuffer = await crypto.subtle.digest('SHA-1', blobBuffer)
    const sha1 = [...new Uint8Array(cryptoBuffer)].map(b => b.toString(16).padStart(2, '0')).join('')
    const status = await this.upload(blob, sha1, partNumber, sendChunkStarted)
    if (status !== 200) {
      throw new Error('Failed chunk upload')
    }
  }

  private upload(blob: Blob, sha1: string, partNumber: number, sendChunkStarted: () => void) {
    // uploading each part with its pre-signed URL
    return new Promise((resolve, reject) => {
      if (!window.navigator.onLine) {
        reject(new Error('System is offline'))
      }

      const xhr = (this.activeConnections[partNumber - 1] = new XMLHttpRequest())
      xhr.timeout = this.timeout
      sendChunkStarted()

      const progressListener = this.handleProgress.bind(this, partNumber - 1, blob.size)

      xhr.upload.addEventListener('progress', progressListener)

      xhr.addEventListener('error', progressListener)
      xhr.addEventListener('abort', progressListener)
      xhr.addEventListener('loadend', progressListener)

      const abortXHR = () => xhr.abort()

      xhr.onreadystatechange = () => {
        if (xhr.readyState === 4 && xhr.status === 200) {
          this.uploadedParts.push({
            partNumber,
            sha1,
          })
          resolve(xhr.status)
          delete this.activeConnections[partNumber - 1]
          window.removeEventListener('offline', abortXHR)
        }
      }
      xhr.onerror = (error: ProgressEvent) => {
        delete this.activeConnections[partNumber - 1]
        reject(error)
        window.removeEventListener('offline', abortXHR)
      }
      xhr.ontimeout = (error: ProgressEvent) => {
        delete this.activeConnections[partNumber - 1]
        reject(error)
        window.removeEventListener('offline', abortXHR)
      }
      xhr.onabort = () => {
        delete this.activeConnections[partNumber - 1]
        reject(new Error('Upload canceled by user or system'))
      }

      window.addEventListener('offline', abortXHR)

      xhr.open('POST', this.uploadUrl)
      xhr.setRequestHeader('Authorization', this.authorizationToken)
      xhr.setRequestHeader('X-Bz-Part-Number', `${partNumber}`)
      xhr.setRequestHeader('Content-Length', `${blob.size}`)
      xhr.setRequestHeader('X-Bz-Content-Sha1', sha1)
      xhr.send(blob)
    })
  }

  private handleProgress(part: number, total: number, event: ProgressEvent) {
    if (event.type === 'progress' || event.type === 'error' || event.type === 'abort') {
      this.progressCache[part] = event.loaded
    }
    if (event.type === 'uploaded') {
      this.uploadedSize += this.progressCache[part] || 0
      delete this.progressCache[part]
    }

    const inProgress = Object.keys(this.progressCache)
      .map(Number)
      .reduce((memo, id) => (memo += this.progressCache[id]), 0)

    const sent = Math.min(this.uploadedSize + inProgress, total)
    this.onProgressFn({
      sent,
      total,
      percentage: Math.round((sent / total) * 100),
    })
  }
}

export class Uploader {
  private readonly baseURL: string
  private readonly threadsQuantity: number
  private readonly onProgressFn: ProgressFn
  private readonly onErrorFn: ErrorFn
  private sender?: Sender

  constructor({ baseURL, threadsQuantity, onProgressFn = () => {}, onErrorFn = () => {} }: UploaderParams) {
    this.baseURL = baseURL
    this.threadsQuantity = Math.min(threadsQuantity || 5, 15)
    this.onProgressFn = onProgressFn
    this.onErrorFn = onErrorFn
  }

  start(file: File) {
    $fetch<CreateOutput>('/create', {
      baseURL: this.baseURL,
      method: 'POST',
      body: {
        name: file.name,
        size: file.size,
      },
    })
      .then(({ uploadUrl, authorizationToken, fileId, parts }) => {
        this.sender = new Sender({
          baseURL: this.baseURL,
          threadsQuantity: this.threadsQuantity,
          onProgressFn: this.onProgressFn,
          onErrorFn: this.onErrorFn,
          uploadUrl,
          authorizationToken,
          file,
          fileId,
          parts,
        })
        this.sender.sendNext()
      })
      .catch((error) => {
        this.onErrorFn(error)
      })
  }

  abort() {
    this.sender?.abort()
  }
}
