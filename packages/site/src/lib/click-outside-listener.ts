const eventName = 'mousedown'

export function listenForClickOutsideRefs(refs: Ref<HTMLElement>[], callback: (e?: Event) => void) {
  const elements = computed(() => refs.map(toValue))
  let listenerFunc = listenForClickOutsideElements(elements.value, callback)
  watch(() => elements, () => {
    if (listenerFunc) {
      stopListeningForClickOutside(listenerFunc)
    }
    listenerFunc = listenForClickOutsideElements(elements.value, callback)
  }, { deep: true })
  return () => {
    if (listenerFunc) {
      stopListeningForClickOutside(listenerFunc)
    }
  }
}

export function listenForClickOutsideElements(elements: HTMLElement | HTMLElement[], callback: (e?: Event) => void) {
  if (!elements) {
    return
  }
  if (!Array.isArray(elements)) {
    elements = [elements]
  }
  const listenerFunc = makeListenerFunc(elements, callback)
  document.addEventListener(eventName, listenerFunc)
  return listenerFunc
}

export function stopListeningForClickOutside(listenerFunc: (event: Event) => void) {
  document.removeEventListener(eventName, listenerFunc)
}

function makeListenerFunc(elements: HTMLElement[], callback: (e?: Event) => void) {
  return (event: Event) => {
    const isClickInside = !!elements.find((el) => {
      return (el instanceof HTMLElement) && el.contains(event.target as Node)
    })
    if (isClickInside) {
      return
    }
    if (typeof callback === 'function') {
      callback(event)
    }
  }
}
