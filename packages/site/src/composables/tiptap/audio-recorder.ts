import { Node, mergeAttributes, VueNodeViewRenderer } from '@tiptap/vue-3'
import AudioRecorderView from '~/components/tiptap/views/AudioRecorderView.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    recorder: {
      startRecorder: () => ReturnType
    }
  }
}

export const AudioRecorder = Node.create<{ HTMLAttributes: Record<string, unknown> }>({
  name: 'recorder',
  group: 'block',
  draggable: true,
  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      [`data-${this.name}`]: {
        default: null,
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: `div[data-${this.name}]`,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]
  },

  addNodeView() {
    return VueNodeViewRenderer(AudioRecorderView)
  },

  addCommands() {
    return {
      startRecorder: () => ({ editor, commands: { insertContent, updateAttributes } }) => {
        if (editor.isActive(this.name)) {
          return updateAttributes(this.name, { [`data-${this.name}`]: true })
        }
        else {
          editor.setEditable(false)
          return insertContent({
            type: this.name,
            attrs: { [`data-${this.name}`]: true },
          })
        }
      },
    }
  },
})
