import { Node, mergeAttributes, VueNodeViewRenderer } from '@tiptap/vue-3'
import AudioPlayerView from '~/components/tiptap/views/AudioPlayerView.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    'audio-player': {
      addAudio: (_src: string, _wave: string, _duration: number) => ReturnType
    }
  }
}

export const AudioPlayer = Node.create<{ HTMLAttributes: Record<string, any> }>({
  name: 'audio-player',
  group: 'block',
  draggable: true,
  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  parseHTML() {
    return [
      {
        tag: 'audio',
      },
    ]
  },

  addAttributes() {
    return {
      src: {
        default: null,
        parseHTML: (element: HTMLAudioElement) => {
          return element.src
        },
      },
      dataWave: {
        default: null,
        parseHTML: (element) => {
          return element.dataset.wave
        },
      },
    }
  },

  renderHTML({ HTMLAttributes }) {
    return ['audio', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]
  },

  addNodeView() {
    return VueNodeViewRenderer(AudioPlayerView)
  },

  addCommands() {
    return {
      addAudio: (src: string, wave: string, duration: number) => ({ editor, commands: { insertContent, updateAttributes } }) => {
        if (editor.isActive(this.name)) {
          return updateAttributes(this.name, { src, 'data-wave': wave, 'data-duration': duration })
        }
        else {
          return insertContent({
            type: this.name,
            attrs: { src, 'data-wave': wave, 'data-duration': duration },
          })
        }
      },
    }
  },
})
