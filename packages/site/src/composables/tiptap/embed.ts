import { Node, mergeAttributes, VueNodeViewRenderer } from '@tiptap/vue-3'
import { <PERSON>lug<PERSON>, Plugin<PERSON>ey } from '@tiptap/pm/state'
import { find } from 'linkifyjs'
import TiptapViewsEmbed from '~/components/tiptap/views/TiptapViewsEmbed.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    embed: {
      setEmbed: (link: string) => ReturnType
    }
  }
}

const Embed = Node.create<{ HTMLAttributes: Record<string, unknown> }>({
  name: 'embed',
  group: 'block',
  draggable: true,
  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  parseHTML() {
    return [
      {
        tag: 'a[href][data-embed-id]',
      },
    ]
  },

  addAttributes() {
    return {
      'href': {
        default: null,
      },
      'data-embed-id': {
        default: () => `embed-${crypto.randomUUID()}`,
      },
    }
  },

  renderHTML({ HTMLAttributes }) {
    return ['a', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]
  },

  addNodeView() {
    return VueNodeViewRenderer(TiptapViewsEmbed)
  },

  addCommands() {
    return {
      setEmbed: (link: string) => ({ editor, commands: { insertContent, updateAttributes } }) => {
        if (editor.isActive(this.name)) {
          return updateAttributes(this.name, { href: link })
        }
        else {
          return insertContent({
            type: this.name,
            attrs: {
              href: link,
              ['data-embed-id']: `embed-${crypto.randomUUID()}`,
            },
            content: [],
          })
        }
      },
    }
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('handlePasteLink'),
        props: {
          handlePaste: (_view, _event, slice) => {
            let textContent = ''
            slice.content.forEach((node) => {
              textContent += node.textContent
            })
            if (!textContent) {
              return false
            }

            const link = find(textContent).find(item => item.isLink && item.value === textContent)
            if (!link) {
              return false
            }

            this.editor.commands.setEmbed(link.href)
            return true
          },
        },
      }),
    ]
  },
})

export default Embed
