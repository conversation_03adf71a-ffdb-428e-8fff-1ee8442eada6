export interface TransitionEvent {
  selector?: string
  propertyName?: string
}

export type PropertyString = 'height' | 'width' | 'transform'
export interface SmoothOptions {
  property?: PropertyString | PropertyString[]
  transition?: string
  transitionEvent?: TransitionEvent
  observeMutations?: boolean
  hideOverflow?: boolean
  debug?: boolean
}
export type SmoothRect = {
  top: number
  right: number
  bottom: number
  left: number
  width: number
  height: number
  x: number
  y: number
}
type State = 'INACTIVE' | 'ACTIVE'

const defaultDurationSeconds = 0.5
const defaultOptions: SmoothOptions & { property: PropertyString } = {
  // Valid values: height, width, transform
  property: 'height',
  // Selector string that will emit a transitionend event.
  // Note that you can specify multiple transitionend
  // event emitters through the use of commas.
  transitionEvent: undefined,
  observeMutations: false,
  // Hide scrollbar during transition. This should be on 99% of the time.
  hideOverflow: true,
  debug: true,
}

export function useSmoothReflow(elRef: Ref<HTMLElement | undefined>, options?: SmoothOptions) {
  options = prepareOptions(options)
  let smoothEl: HTMLElement | null
  const properties = Array.isArray(options?.property) ? options.property : [options?.property ?? defaultOptions.property]
  let beforeRect: ReturnType<typeof getBoundingClientRect> | null = null
  let state: State
  let shouldBeRemoved: boolean
  const listenerOpts: AddEventListenerOptions & EventListenerOptions = { passive: true }
  let mutationObserver: MutationObserver | null
  const STATES: Record<string, State> = {
    INACTIVE: 'INACTIVE',
    ACTIVE: 'ACTIVE',
  }

  function prepareOptions(options?: SmoothOptions) {
    const o = { ...defaultOptions, ...options }
    const properties = Array.isArray(options?.property) ? options.property : [options?.property ?? defaultOptions.property]
    if (!o.transition) {
      o.transition = properties.map(propName => `${propName} ${defaultDurationSeconds}s`).join(',')
    }
    return o
  }

  function unsmoothReflow() {
    unregisterElement()
  }

  onBeforeMount(() => {
    // removeSmoothElement()
  })

  onMounted(() => {
    setSmoothElement()
    setBeforeValues()
  })

  onBeforeUnmount(() => {
    smoothEl?.removeEventListener('transitionend', endTransitionListener, listenerOpts)
  })

  onBeforeUpdate(() => {
    if (!smoothEl) {
      setSmoothElement()
    }
    setBeforeValues()
  })

  onUpdated(() => {
    nextTick(() => {
      // if (!smoothEl) { return }
      // Retrieve element on demand
      // It could have been hidden by v-if/v-show
      setSmoothElement()
      doSmoothReflow()
      flushRemoved()
    })
  })

  // watch(elRef, (val) => {
  //   if (val instanceof Element) {
  //     setSmoothElement()
  //     setBeforeValues()
  //     doSmoothReflow()
  //   } else {
  //     scheduleRemoval()
  //     flushRemoved()
  //   }
  // })

  function setSmoothElement() {
    if (!elRef.value) {
      return
    }
    smoothEl = elRef.value
    smoothEl?.addEventListener('transitionend', endTransitionListener, listenerOpts)
    if (options?.observeMutations) {
      setupObserver(smoothEl)
    }
  }

  function removeSmoothElement() {
    removeObserver()
    smoothEl?.removeEventListener('transitionend', endTransitionListener, listenerOpts)
    beforeRect = null
    smoothEl = null
  }

  let compTransition: string
  let compOverflowX: string
  let compOverflowY: string
  // Save the DOM properties of the $smoothEl before the data update

  function setBeforeValues() {
    if (!smoothEl) {
      return
    }
    const { transition, overflowX, overflowY } = window.getComputedStyle(smoothEl) ?? {}
    compTransition = transition
    compOverflowX = overflowX
    compOverflowY = overflowY
    beforeRect = getBoundingClientRect(smoothEl)
    // Important to stopTransition after we've saved this.beforeRect
    if (state === STATES.ACTIVE) {
      stopTransition()
      debug('Transition was interrupted.')
    }
  }

  function didValuesChange(beforeRect: SmoothRect, afterRect: SmoothRect) {
    const b = beforeRect
    const a = afterRect
    // There's nothing to transition from.
    if (Object.keys(beforeRect).length === 0) {
      return false
    }
    for (const prop of properties) {
      if (prop === 'transform'
        && (b.top !== a.top || b.left !== a.left)) {
        return true
      }
      else if (prop !== 'transform' && b[prop] !== a[prop]) {
        return true
      }
    }
    return false
  }

  function doSmoothReflow(event: Event | string = 'data update') {
    if (!smoothEl) {
      if (elRef.value) {
        setSmoothElement()
        setBeforeValues()
      }
      else {
        debug('Could not find registered el to perform doSmoothReflow.')
        state = STATES.INACTIVE
        return
      }
    }
    // A transition is already occurring, don't interrupt it.
    if (state === STATES.ACTIVE) {
      return
    }
    state = STATES.ACTIVE

    const triggeredBy = (typeof event === 'string') ? event : event.target
    debug('doSmoothReflow triggered by:', triggeredBy)

    if (!beforeRect) {
      beforeRect = getBoundingClientRect(smoothEl)
    }
    const afterRect = getBoundingClientRect(smoothEl)
    if (!didValuesChange(beforeRect, afterRect)) {
      debug('Property values did not change.')
      state = STATES.INACTIVE
      return
    }
    debug('beforeRect', beforeRect)
    debug('afterRect', afterRect)

    saveOverflowValues(compOverflowX, compOverflowY)

    for (const prop of properties) {
      if (prop === 'transform') {
        const invertLeft = beforeRect.left - afterRect.left
        const invertTop = beforeRect.top - afterRect.top
        smoothEl.style.transform = `translate(${invertLeft}px, ${invertTop}px)`
      }
      else {
        smoothEl.style[prop] = beforeRect[prop] + 'px'
      }
    }

    smoothEl.offsetHeight // Force reflow

    smoothEl.style.transition = [compTransition, options?.transition].filter(d => d).join(',')
    for (const prop of properties) {
      if (prop === 'transform') {
        smoothEl.style.transform = ''
      }
      else {
        smoothEl.style[prop] = afterRect[prop] + 'px'
      }
    }
    // Transition is now started.
  }

  function endTransitionListener(event: Event & TransitionEvent) {
    const targetEl = event.target
    // Transition on smooth element finished
    if (smoothEl === targetEl) {
      // The transition property is one that was registered
      if (properties.includes(event.propertyName as PropertyString)) {
        stopTransition()
        // Record the beforeValues AFTER the data change, but potentially
        // BEFORE any transitionend events.
        if (hasRegisteredEventEmitter()) {
          setBeforeValues()
        }
      }
    }
    else if (isRegisteredEventEmitter(event)) {
      doSmoothReflow(event)
    }
  }

  function hasRegisteredEventEmitter() {
    const { transitionEvent } = options ?? {}
    if (!transitionEvent) {
      return false
    }
    return transitionEvent !== null && Object.keys(transitionEvent).length > 0
  }

  // Check if we should perform doSmoothReflow() after a transitionend event.
  function isRegisteredEventEmitter(event: Event) {
    if (!hasRegisteredEventEmitter()) {
      return false
    }
    const targetEl = event.target as Element
    const { selector, propertyName } = options?.transitionEvent ?? {}
    if (propertyName != null && propertyName !== (event as TransitionEvent).propertyName) {
      return false
    }
    // '!= null' coerces the type to also check for undefined.
    if (selector != null && !targetEl?.matches(selector)) {
      return false
    }

    // If 'transform' isn't a registered property,
    // then we don't need to act on any transitionend
    // events that occur outside the $smoothEl
    if (!properties.includes('transform')) {
      // Checks if $targetEl IS or WAS a descendent of $smoothEl.
      let smoothElContainsTarget = false
      // composedPath is missing in ie/edge of course.
      const path = event.composedPath ? event.composedPath() : []
      for (const el of path) {
        if (smoothEl === el) {
          smoothElContainsTarget = true
          break
        }
      }
      if (!smoothElContainsTarget) {
        return false
      }
    }
    return true
  }

  function saveOverflowValues(overflowX: string, overflowY: string) {
    if (!options?.hideOverflow || !smoothEl) {
      return
    }
    // save overflow properties before overwriting
    compOverflowX = overflowX
    compOverflowY = overflowY
    smoothEl.style.overflowX = 'hidden'
    smoothEl.style.overflowY = 'hidden'
  }

  function restoreOverflowValues() {
    if (!options?.hideOverflow || !smoothEl) {
      return
    }
    // Restore original overflow properties
    smoothEl.style.overflowX = compOverflowX
    smoothEl.style.overflowY = compOverflowY
  }

  function stopTransition() {
    if (!properties || !smoothEl) {
      return
    }
    // Change prop back to auto
    for (const prop of properties) {
      smoothEl.style[prop] = ''
    }
    restoreOverflowValues()
    // Clean up inline transition
    smoothEl.style.transition = ''
    state = STATES.INACTIVE
  }

  function scheduleRemoval() {
    shouldBeRemoved = true
  }

  function debug(...args: any[]) {
    if (!options?.debug) {
      return
    }
    const debugArgs = ['VSR_DEBUG:'].concat(Array.from(args))
    console.log.apply(null, debugArgs)
  }

  function flushRemoved() {
    if (!shouldBeRemoved) {
      return
    }
    stopTransition()
    removeSmoothElement()
  }

  function unregisterElement() {
    // Don't remove right away, as it might be in the middle of
    // a doSmoothReflow, and leave the element in a broken state.
    scheduleRemoval()
  }

  function setupObserver(el: HTMLElement) {
    // Create an observer instance
    mutationObserver = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === 'childList') {
          doSmoothReflow()
        }
        // else if (mutation.type === 'attributes') {
        //   doSmoothReflow()
        // }
      }
    })
    const config = { childList: true, subtree: true, attributes: false }
    mutationObserver.observe(el, config)
  }
  function removeObserver() {
    mutationObserver?.disconnect()
    mutationObserver = null
  }

  return {
    unsmoothReflow,
  }
}

// Converts DOMRect into plain object.
// Overflow is temporarily forced to 'hidden' to prevent margin collapse,
// and receive an accurate height/width value.
const getBoundingClientRect = (el: HTMLElement) => {
  const overflow = el.style.overflow
  el.style.overflow = 'hidden'
  const { top, right, bottom, left, width, height, x, y } = el.getBoundingClientRect()
  el.style.overflow = overflow
  return { top, right, bottom, left, width, height, x, y }
}
