import { cloneDeep } from 'lodash-es'

interface StreamMessage<T> {
  type?: string
  payload?: T
}

export async function usePaginatorFetch<T extends Record<string, any>>(
  key: string,
  url: MaybeRefOrGetter<string>,
  keyProp: keyof T,
  stream: Ref<StreamMessage<T> | undefined> = ref<StreamMessage<T>>(),
  onFn?: (eventType: 'create' | 'update' | 'delete', item: T) => void,
  sortFn?: (a: T, b: T) => number,
) {
  const maxId = ref<T[keyof T] | undefined>()
  const items = ref<T[]>([])

  const { data, ...rest } = await useFetch<T[]>(url, { key, params: { max_id: maxId } })

  watch(data, (value) => {
    if (Array.isArray(value)) {
      if (!maxId.value) {
        items.value = []
      }
      onCreate(value)
    }
  }, { immediate: true })

  function loadNext() {
    maxId.value = items.value.at(-1)?.[keyProp]
  }

  function getEventType(type?: string) {
    switch (type) {
      case 'create':
        return 'create'
      case 'update':
        return 'update'
      case 'delete':
        return 'delete'
    }
  }

  function onCreate(payloads: T[]) {
    const records = cloneDeep(toRaw(items.value))
    for (const payload of payloads) {
      const index = records.length ? records.findIndex(record => record[keyProp] === payload?.[keyProp]) : -1
      if (index === -1) {
        records.push(payload)
        onFn?.('create', payload)
      }
    }
    items.value = sortFn ? records.sort(sortFn) : records
  }

  function onUpdate(payload: T) {
    const records = cloneDeep(toRaw(items.value))
    const index = records.length ? records.findIndex(record => record[keyProp] === payload?.[keyProp]) : -1
    if (index >= 0) {
      records[index] = payload
    }
    else {
      records.push(payload)
    }
    items.value = sortFn ? records.sort(sortFn) : records
    onFn?.('update', payload)
  }

  function onDelete(payload: T) {
    const records = cloneDeep(toRaw(items.value))
    if (records.length) {
      const index = records.findIndex(record => record[keyProp] === payload?.[keyProp])
      if (index >= 0) {
        records.splice(index, 1)
        items.value = sortFn ? records.sort(sortFn) : records
        onFn?.('delete', payload)
      }
    }
  }

  watch(stream, (message) => {
    const { type, payload } = message ?? {}
    const eventType = getEventType(type)
    if (eventType && payload) {
      if (eventType === 'create') {
        onCreate([payload])
      }
      if (eventType === 'update') {
        onUpdate(payload)
      }
      if (eventType === 'delete') {
        onDelete(payload)
      }
    }
  })

  return {
    items: computed(() => items.value),
    loadNext,
    ...rest,
  }
}

export function useEndAnchor(loadNext: () => void) {
  const endAnchor = ref<HTMLDivElement>()
  if (import.meta.client) {
    const bound = useElementBounding(endAnchor)
    const isInScreen = computed(() => {
      // Check if we're near the bottom of the content
      const isNearBottom = bound.top.value < window.innerHeight * 2
      // Check if we're at the bottom of the page
      const isAtBottom = window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 100
      // Check if content is shorter than viewport
      const isShortContent = document.documentElement.scrollHeight <= window.innerHeight
      return isNearBottom || isAtBottom || isShortContent
    })
    const deactivated = useDeactivated()
    // Initial load check
    let initialLoadDone = false
    onMounted(() => {
      // Force an initial update of bounds
      bound.update()
      // Use a small delay to ensure everything is initialized
      setTimeout(() => {
        if (!initialLoadDone && !deactivated.value) {
          initialLoadDone = true
          loadNext()
        }
      }, 100)
    })
    // Watch for scroll and resize
    useIntervalFn(() => {
      bound.update()
    }, 1000)

    watch(
      isInScreen,
      () => {
        if (
          isInScreen.value
          // No new content is loaded when the keepAlive page enters the background
          && deactivated.value === false
        ) {
          loadNext()
        }
      },
    )
  }
  return endAnchor
}
