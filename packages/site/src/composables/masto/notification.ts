import type { WsEvents } from 'masto'
import type { Marker } from '~/server/utils/backend/markers'

type StoreType = Record<string, undefined | [Promise<WsEvents>, string[]]>
const records = reactive<StoreType>({})

export function useNotifications() {
  const id = useAuth().account.value?.id

  const { client, canStreaming } = $(useMasto())

  async function clearNotifications() {
    const record = id ? records[id] : undefined
    if (!record) {
      return
    }
    const lastReadId = record[1][0]
    record[1] = []
    if (lastReadId) {
      await $fetch('/api/markers', { method: 'POST', body: { timeline: 'notifications', lastReadId } })
    }
  }

  async function connect(): Promise<void> {
    const record = id ? records[id] : undefined
    if (!isHydrated.value || !record || !useAuth().loggedIn.value) {
      return
    }

    let resolveStream
    const stream = new Promise<WsEvents>((resolve) => {
      resolveStream = resolve
    })
    records[id] = [stream, []]

    await until($$(canStreaming)).toBe(true)

    client.v1.stream.streamUser().then(resolveStream)
    stream.then(s => s.on('notification', (n) => {
      if (records[id]) {
        records[id]![1].unshift(n.id)
      }
    }))

    const markers = await $fetch<Marker[]>('/api/markers', { query: { timeline: ['notifications'] } })
    const lastReadId = markers.shift()?.lastReadId
    const paginator = client.v1.notifications.list({ limit: 30 })
    do {
      const result = await paginator.next()
      if (!result.done && result.value.length) {
        for (const notification of result.value) {
          if (notification.id === lastReadId) {
            return
          }
          records[id]![1].push(notification.id)
        }
      }
      else {
        break
      }
    } while (true)
  }

  function disconnect(): void {
    if (!id || !records[id]) {
      return
    }
    records[id]![0].then(stream => stream.disconnect())
    records[id] = undefined
  }

  watch(currentUser, disconnect)

  onHydrated(() => {
    connect()
  })

  return {
    notifications: computed(() => id ? records[id]?.[1].length ?? 0 : 0),
    disconnect,
    clearNotifications,
  }
}
