{"a11y": {"loading_page": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "loading_titled_page": "<PERSON><PERSON> {0} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>", "locale_changed": "<PERSON><PERSON>, yeni dil: {0}", "locale_changing": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "route_loaded": "Sayfa {0} yüklendi"}, "account": {"avatar_description": "{0} avatarı", "blocked_by": "Bu kullanıcı sizi engellemiş", "blocked_domains": "Engel<PERSON>n alan <PERSON>", "blocked_users": "Engellenen kullanıcılar", "blocking": "<PERSON><PERSON><PERSON>", "bot": "BOT", "favourites": "<PERSON><PERSON><PERSON><PERSON>", "follow": "Ta<PERSON><PERSON> et", "follow_back": "<PERSON><PERSON> et", "follow_requested": "İstek gönderildi", "followers": "Takipçiler", "followers_count": "{0} Takipçi", "following": "<PERSON><PERSON><PERSON> ed<PERSON>", "following_count": "{0} takip edilen", "follows_you": "Seni takip ediyor", "go_to_profile": "Profile git", "joined": "Katıldı", "moved_title": "belirttiği yeni hesabı:", "muted_users": "Susturulmuş kullanıcılar", "muting": "Susturulmuş", "mutuals": "Karşılıklı takip", "pinned": "<PERSON><PERSON><PERSON><PERSON>", "posts": "<PERSON><PERSON><PERSON><PERSON>", "posts_count": "{0} <PERSON><PERSON><PERSON><PERSON>", "profile_description": "{0} profil b<PERSON>şlı<PERSON>ı", "profile_unavailable": "Profil mevcut de<PERSON>", "unblock": "<PERSON><PERSON><PERSON> ka<PERSON>", "unfollow": "<PERSON><PERSON><PERSON> bırak", "unmute": "Susturulmayı kaldır"}, "action": {"apply": "<PERSON><PERSON><PERSON><PERSON>", "bookmark": "Yer im<PERSON>ine ekle", "bookmarked": "Yer im<PERSON>ine e<PERSON>ndi", "boost": "Boost", "boost_count": "{0}", "boosted": "Boost edildi", "clear_upload_failed": "<PERSON><PERSON><PERSON> y<PERSON><PERSON>me hatalarını temizle", "close": "Ka<PERSON><PERSON>", "compose": "Oluştur", "confirm": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "enter_app": "Uygulamaya gir", "favourite": "<PERSON>av<PERSON><PERSON>e ekle", "favourite_count": "{0}", "favourited": "Favorilere e<PERSON>ndi", "more": "<PERSON><PERSON> fazla", "next": "<PERSON><PERSON><PERSON>", "prev": "<PERSON><PERSON><PERSON>", "publish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reply": "<PERSON>va<PERSON> ver", "reply_count": "{0}", "reset": "Sıfırla", "save": "<PERSON><PERSON>", "save_changes": "Değişikleri kaydet", "sign_in": "<PERSON><PERSON><PERSON> yap", "switch_account": "<PERSON><PERSON><PERSON>", "vote": "Oy ver"}, "app_desc_short": "Hızlı bir Mastodon web istemcisi", "app_logo": "Elk Logosu", "app_name": "Elk", "attachment": {"edit_title": "<PERSON><PERSON>ı<PERSON><PERSON>", "remove_label": "<PERSON><PERSON> kaldı<PERSON>"}, "command": {"activate": "Etkinleştir", "complete": "<PERSON><PERSON><PERSON>", "compose_desc": "<PERSON>ni bir gönderi yaz", "n-people-in-the-past-n-days": "geçen {1} gündeki {0} kişi", "select_lang": "<PERSON><PERSON> <PERSON><PERSON>", "sign_in_desc": "Var olan bir hesap ekle", "switch_account": "{0} hesab<PERSON>na geç", "switch_account_desc": "Başka bir hesaba geç", "toggle_dark_mode": "Karanlık mod durumunu de<PERSON>ştir", "toggle_zen_mode": "Zen mod du<PERSON><PERSON><PERSON>"}, "common": {"end_of_list": "Listenin sonu", "error": "HATA", "in": "içinde", "not_found": "404 Bulunamadı", "offline_desc": "Çevrimdışısınız gibi görünüyor. Lütfen internet bağlantınızı kontrol edin."}, "compose": {"draft_title": "Taslak {0}", "drafts": "Taslaklar ({v})"}, "confirm": {"common": {"cancel": "Hay<PERSON><PERSON>", "confirm": "<PERSON><PERSON>"}, "delete_posts": {"cancel": "İptal et", "confirm": "Sil", "title": "<PERSON>u gönderiyi silmek istediğinizden emin misiniz?"}}, "conversation": {"with": "ile"}, "error": {"account_not_found": "Hesap {0} bulunamadı", "explore-list-empty": "<PERSON><PERSON> anda hi<PERSON> değil. Daha sonra tekrar kontrol edin!", "file_size_cannot_exceed_n_mb": "<PERSON><PERSON><PERSON> {0}MB'ı geçemez", "sign_in_error": "<PERSON><PERSON><PERSON>a bağlanılamadı.", "status_not_found": "G<PERSON><PERSON><PERSON> bulunamadı", "unsupported_file_format": "Desteklenmeyen dosya biçimi"}, "help": {"desc_highlight": "Orada burada bir kaç hata ve eksik özellik bekleyin.", "desc_para1": "Elk'i, bizim çalışması devam eden Mastodon web istemcimizi, denemedeki ilginiz için teşekkürler!", "desc_para2": "Zaman içinde geliştirmek ve iyileştirmek için çok çalışıyoruz.", "desc_para3": "Geliştirmemizi hızlandırmak için takıma GitHub Sponsors üzerinden sponsor olabilirsinizi. Umarız Elk'i beğenirsiniz!", "desc_para4": "Elk açık kaynaklıdır. Test etmek, geri d<PERSON> vermek veya katkıda bulunmak isterseniz,", "desc_para5": "GitHub'da bize ul<PERSON>ın", "desc_para6": "ve dahil olun.", "title": "Elk ön izlemede!"}, "language": {"search": "Ara"}, "menu": {"block_account": "<PERSON><PERSON><PERSON> {0}", "block_domain": "<PERSON> adı {0} engel<PERSON>", "copy_link_to_post": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> linkini k<PERSON>ala", "delete": "Sil", "delete_and_redraft": "Sil & yeniden taslak yap", "direct_message_account": "{0} <PERSON><PERSON> mesaj <PERSON>ö<PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "hide_reblogs": "{0} boostlarını gizle", "mention_account": "{0} etiketle", "mute_account": "{0} sustur", "mute_conversation": "<PERSON><PERSON> g<PERSON><PERSON> sustur", "open_in_original_site": "Orijinal sitede aç", "pin_on_profile": "<PERSON><PERSON>", "share_post": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "show_favourited_and_boosted_by": "Favoriye ekleyenleri ve boost edenleri göster", "show_reblogs": "{0} boostlarını göster", "show_untranslated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ha<PERSON>", "toggle_theme": {"dark": "Karanlık mod durumunu de<PERSON>ştir", "light": "Aydınlık mod durumunu değiştir"}, "translate_post": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unblock_account": "{0} en<PERSON><PERSON> ka<PERSON>", "unblock_domain": "<PERSON> adı {0} en<PERSON><PERSON> kaldır", "unmute_account": "{0} se<PERSON><PERSON> a<PERSON>", "unmute_conversation": "G<PERSON><PERSON><PERSON>n sesini aç", "unpin_on_profile": "Profildeki sabiti kaldır"}, "nav": {"back": "<PERSON><PERSON> git", "blocked_domains": "Engel<PERSON>n alan <PERSON>", "blocked_users": "Engellenen kullanıcılar", "bookmarks": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "built_at": "{0} <PERSON><PERSON><PERSON>", "conversations": "Konuşmalar", "explore": "Keşfet", "favourites": "<PERSON><PERSON><PERSON><PERSON>", "federated": "Federe", "home": "Ev", "local": "<PERSON><PERSON>", "muted_users": "Susturulmuş kullanıcılar", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "profile": "Profil", "search": "Ara", "select_feature_flags": "Özelliklerin <PERSON>", "select_font_size": "<PERSON><PERSON>", "select_language": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "show_intro": "<PERSON><PERSON><PERSON><PERSON>", "toggle_theme": "Temayı Değiştir", "zen_mode": "Zen Modu"}, "notification": {"favourited_post": "<PERSON><PERSON><PERSON><PERSON>", "followed_you": "<PERSON><PERSON> takip etti", "followed_you_count": "{0} ki<PERSON>i seni takip etti", "missing_type": "EKSİK notification.type:", "reblogged_post": "g<PERSON>nderini yeniden blogladı", "request_to_follow": "Takip isteği attı", "signed_up": "<PERSON><PERSON><PERSON><PERSON>", "update_status": "Gönderisini güncelledi"}, "placeholder": {"content_warning": "Uyarını buraya yaz", "default_1": "Ak<PERSON>ında ne var?", "reply_to_account": "{0} cevap ver", "replying": "<PERSON>vap veriliyor", "the_thread": "konu"}, "pwa": {"dismiss": "Görmezden gel", "title": "Yeni Elk güncellemesi mevcut!", "update": "<PERSON><PERSON><PERSON><PERSON>", "update_available_short": "Elk'i güncelle", "webmanifest": {"canary": {"description": "Hızlı bir Mastodon web istemcisi (canary)", "name": "Elk (canary)", "short_name": "Elk (canary)"}, "dev": {"description": "Hızlı bir Mastodon web istemcisi (dev)", "name": "Elk (dev)", "short_name": "Elk (dev)"}, "preview": {"description": "Hızlı bir Mastodon web istemcisi (preview)", "name": "Elk (preview)", "short_name": "Elk (preview)"}, "release": {"description": "Hızlı bir Mastodon web istemcisi", "name": "Elk", "short_name": "Elk"}}}, "search": {"search_desc": "İnsanları & etiketleri ara", "search_empty": "Bu arama için sonuç bulu<PERSON>ı"}, "settings": {"about": {"label": "Hakkında", "meet_the_team": "Takım ile buluş", "sponsor_action": "Bize spponsor ol", "sponsor_action_desc": "Elk'i geliştiren takıma destek olmak için", "sponsors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sponsors_body_1": "Elk cömert sponsorluk ve şunların yardımı sayesinde mümkün oldu:", "sponsors_body_2": "Ve Elk takımına ve üyelerine sponsor olan tüm şirketler ve şahıslar.", "sponsors_body_3": "Eğer uygulamadan hoşlandıysanız bize sponsor olmayı düşünün:"}, "account_settings": {"description": "Mastodon UI'da hesap a<PERSON>ını değiştir", "label": "<PERSON><PERSON><PERSON>"}, "interface": {"color_mode": "Renk Modu", "dark_mode": "Karanlık Mod", "default": " (varsayılan)", "font_size": "<PERSON><PERSON>", "label": "Arayüz", "light_mode": "Aydınlık Mod", "system_mode": "Sistem"}, "language": {"display_language": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Dil"}, "notifications": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notifications": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "push_notifications": {"alerts": {"favourite": "<PERSON><PERSON><PERSON><PERSON>", "follow": "<PERSON><PERSON>", "mention": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "poll": "<PERSON><PERSON><PERSON>", "reblog": "Gönderinizi yeniden bloglamalar", "title": "Hangi bildirimleri alacaksınız??"}, "description": "Elk'i kullanmıyorken bile bildirimleri alın.", "instructions": "@:settings.notifications.push_notifications.save_settings butonunu kull<PERSON>ikleri kaydetmeyi unutmayın!", "label": "Anlık bildirim <PERSON>ı", "policy": {"all": "Herkesden", "followed": "Takip ettiğ<PERSON> k<PERSON>", "follower": "Takipçilerden", "none": "<PERSON><PERSON><PERSON>", "title": "Kimden bildirim alabilirim??"}, "save_settings": "Ayarları kaydet", "subscription_error": {"clear_error": "Hat<PERSON>ı temizle", "permission_denied": "<PERSON><PERSON><PERSON><PERSON> engellendi: tarayıcınızda bildirimleri etkinleştirin.", "request_error": "Abonelik talep edilirken bir hata <PERSON>, tekrar deneyin ve hata devam ederse lütfen sorunu Elk deposuna bildirin.", "title": "Anlık bildirimlere abone olunamadı", "too_many_registrations": "Tarayıcı kısıtlamaları nedeniyle Elk, farklı sunuculardaki birden çok hesap için anlık bildirimler hizmetini kullanamaz. Başka bir hesaptaki anlık bildirim aboneliğinden çıkmalı ve tekrar denemelisiniz."}, "title": "Anlık bildirim <PERSON>ı", "undo_settings": "Değişiklikleri geri al", "unsubscribe": "Anlık bildirimleri devre dışı bırak", "unsupported": "Tarayıcınız anlık bildirimleri desteklemiyor.", "warning": {"enable_close": "Ka<PERSON><PERSON>", "enable_description": "Elk açık değilken bildirim almak için anlık bildirimleri etkinleştirin. Etkinleştirildikten sonra yukarıdaki \"@:settings.notifications.show_btn{'\"'} düğmesini kullanarak tam olarak ne tür etkileşimlerin anlık bildirimler oluşturduğunu kontrol edebilirsiniz.", "enable_description_desktop": "Elk açık değilken bildirim almak için anlık bildirimleri etkinleştirin. Etkinleştirildikten sonra \"Ayarlar > Bildirimler > Anlık bildirim ayarları\"nda hangi tür etkileşimlerin anlık bildirimler oluşturduğunu tam olarak kontrol edebilirsiniz.", "enable_description_mobile": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON> > <PERSON><PERSON><PERSON><PERSON><PERSON> > <PERSON><PERSON><PERSON><PERSON> bildirim ayar<PERSON>\" gez<PERSON>me menüsünü kull<PERSON>rak da erişebilirsiniz.", "enable_description_settings": "Elk açık değilken bildirim almak için anlık bildirimleri etkinleştirin. Etkinleştirdikten sonra, aynı ekranda hangi tür etkileşimlerin anlık bildirimleri oluşturduğunu tam olarak kontrol edebileceksiniz.", "enable_desktop": "Anlık bildirimleri etkinleştir", "enable_title": "Hiçbirşeyi kaçırma", "re_auth": "Görünüşe göre sunucunuz anlık bildirimleri desteklemiyor. Çıkış yapmayı deneyin ve tekrar giriş yapın, bu mesaj hala görünüyorsa sunucu yöneticinizle iletişime geçin."}}, "show_btn": "<PERSON><PERSON><PERSON><PERSON> git"}, "notifications_settings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preferences": {"github_cards": "GitHub Cards", "hide_boost_count": "Boost sayısını gizle", "hide_favorite_count": "<PERSON><PERSON><PERSON> say<PERSON>ını gizle", "hide_follower_count": "Takipçi sayısını gizle", "label": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>sel <PERSON>", "user_picker": "Kullanıcı Seçici", "virtual_scroll": "<PERSON><PERSON><PERSON><PERSON>"}, "profile": {"appearance": {"bio": "<PERSON><PERSON>ı<PERSON><PERSON>", "description": "avatar, kullanıcı adı, profil vb. düzenle", "display_name": "Görünen ad", "label": "G<PERSON>rü<PERSON><PERSON><PERSON>", "profile_metadata": "Profil üstverisi", "profile_metadata_desc": "Profilinizde bir tablo olarak görüntülenen en fazla {0} öğeye sahip olabilirsiniz.", "title": "<PERSON><PERSON>"}, "featured_tags": {"description": "İnsanlar bu etiketlerler altında herkese açık gönderilerinize göz atabilir.", "label": "<PERSON>ne <PERSON> etiketler"}, "label": "Profil"}, "select_a_settings": "<PERSON><PERSON> ayar seç", "users": {"export": "Kullanıcı Tokenlerini Dışa Aktar", "import": "Kullanıcı Tokenlerini İçe Aktar", "label": "<PERSON><PERSON>ş yapılan kullanıcılar"}}, "state": {"attachments_exceed_server_limit": "Ek sayısı gönderi başına sınırı aştı.", "attachments_limit_error": "<PERSON><PERSON><PERSON><PERSON> başına sınır aşıldı", "edited": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "editing": "Düzenleniyor", "loading": "Yükleniyor...", "publishing": "Yayımlanıyor", "upload_failed": "Yükleme başarısız", "uploading": "Yükleniyor..."}, "status": {"boosted_by": "Tarafından boostlandı:", "edited": "D<PERSON>zenlendi {0}", "favourited_by": "Tarafından favorilendi:", "filter_hidden_phrase": "Tarafından filtrelendi:", "filter_removed_phrase": "Filtre tarafından silindi", "filter_show_anyway": "<PERSON><PERSON>", "img_alt": {"desc": "<PERSON><PERSON>ı<PERSON><PERSON>", "dismiss": "Görmezden gel"}, "poll": {"count": "{0} oy", "ends": "{0} biter", "finished": "{0} bitti"}, "reblogged": "{0} yeniden blogladı", "replying_to": "{0} cevap veriliyor", "show_full_thread": "<PERSON><PERSON><PERSON> k<PERSON>", "someone": "biri", "spoiler_show_less": "<PERSON><PERSON> a<PERSON> g<PERSON>", "spoiler_show_more": "Daha <PERSON> gö<PERSON>", "thread": "<PERSON><PERSON>", "try_original_site": "Orijinal siteyi dene"}, "status_history": {"created": "{0} oluş<PERSON>uldu", "edited": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tab": {"for_you": "<PERSON><PERSON>", "hashtags": "<PERSON><PERSON><PERSON><PERSON>", "media": "<PERSON><PERSON><PERSON>", "news": "<PERSON><PERSON><PERSON>", "notifications_all": "<PERSON><PERSON><PERSON>", "notifications_mention": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "posts": "<PERSON><PERSON><PERSON><PERSON>", "posts_with_replies": "Gönderiler & Yanıtlar"}, "tag": {"follow": "Ta<PERSON><PERSON> et", "follow_label": "{0} et<PERSON><PERSON><PERSON> takip et", "unfollow": "<PERSON><PERSON><PERSON> bırak", "unfollow_label": "{0} et<PERSON><PERSON><PERSON> ta<PERSON> bırak"}, "time_ago_options": {"day_future": "{n} gün <PERSON>", "day_past": "{n} gün <PERSON><PERSON>", "hour_future": "{n} saat içinde", "hour_past": "{n} saat önce", "just_now": "<PERSON><PERSON><PERSON>", "minute_future": "{n} da<PERSON><PERSON>", "minute_past": "{n} da<PERSON><PERSON>", "month_future": "{n} ay i<PERSON><PERSON>e", "month_past": "{n} ay önce", "second_future": "şimdi|{n} saniye içinde", "second_past": "şimdi|{n} saniye önce", "short_day_future": "{n} g<PERSON><PERSON>", "short_day_past": "{n}d", "short_hour_future": "{n} saatte", "short_hour_past": "{n}h", "short_minute_future": "{n} da<PERSON><PERSON><PERSON>", "short_minute_past": "{n}min", "short_month_future": "{n} ayda", "short_month_past": "{n}mo", "short_second_future": "{n} saniyede", "short_second_past": "{n}s", "short_week_future": "{n} haftada", "short_week_past": "{n}w", "short_year_future": "{n} yılda", "short_year_past": "{n}y", "week_future": "{n} hafta i<PERSON><PERSON>e", "week_past": "{n} hafta önce", "year_future": "{n} yıl <PERSON>e", "year_past": "{n} yıl <PERSON>nce"}, "timeline": {"show_new_items": "{v} yeni <PERSON>", "view_older_posts": "<PERSON><PERSON><PERSON>dan eski gönderiler görüntülenmeyebilir."}, "title": {"federated_timeline": "Federe Edilmiş Zaman Akışı", "local_timeline": "<PERSON><PERSON>"}, "tooltip": {"add_content_warning": "İçerik uyarısı ekle", "add_emojis": "<PERSON><PERSON><PERSON> e<PERSON>", "add_media": "resim, video yada ses dosyası ekle", "add_publishable_content": "Yayımlanacak içerik ekle", "change_content_visibility": "İçerik görünürlüğünü değiştir", "change_language": "<PERSON><PERSON>", "emoji": "<PERSON><PERSON><PERSON>", "explore_links_intro": "<PERSON><PERSON>, <PERSON>u anda merkezi o<PERSON> ağın bu ve diğer sunucularındaki insanlar tarafından konuşuluyor.", "explore_posts_intro": "<PERSON><PERSON>, <PERSON><PERSON> anda merkez<PERSON> o<PERSON> ağın bu ve diğer sunucularındaki insanlar tarafından ilgi görüyor.", "explore_tags_intro": "<PERSON><PERSON>, şu anda merkezi o<PERSON> ağın bu ve diğer sunucularındaki insanlar tarafından ilgi görüyor.", "toggle_code_block": "<PERSON><PERSON> b<PERSON><PERSON><PERSON> du<PERSON>"}, "user": {"add_existing": "Var olan bir hesap ekle", "server_address_label": "<PERSON><PERSON><PERSON>", "sign_in_desc": "Profilleri veya hashtag'leri taki<PERSON>, favor<PERSON><PERSON>, gönderileri paylaşmak ve yanıtlamak veya farklı bir sunucudaki hesabınızdan etkileşim kurmak için oturum açın.", "sign_in_notice_title": "{0} herkese açık veri görüntüleniyor", "sign_out_account": "{0} çık<PERSON>ş yap", "tip_no_account": "<PERSON><PERSON><PERSON> bir Mastodon hesabınız yoksa, {0}.", "tip_register_account": "sunucunuzu seçin ve kaydolun"}, "visibility": {"direct": "Direkt", "direct_desc": "<PERSON><PERSON><PERSON> bah<PERSON><PERSON>n k<PERSON> gö<PERSON>ü<PERSON>ü<PERSON>", "private": "<PERSON><PERSON><PERSON>", "private_desc": "<PERSON><PERSON><PERSON>e gö<PERSON>ü<PERSON>", "public": "Herkese açık", "public_desc": "<PERSON><PERSON><PERSON> gö<PERSON>ü<PERSON>ür", "unlisted": "Liste dışı", "unlisted_desc": "<PERSON><PERSON> ta<PERSON>ı<PERSON>n görülebilir, ancak keşif özellikleri devre dışı bırakılmıştır"}}