import type { mastodon } from '.'
import type { Actor } from '~/server/utils/backend/activitypub/actors'

export interface ConversationMessageData {
  id: string
  conversationId: string
  publishId?: string
  content: string
  createdAt: number
  actor: Actor
}

export interface ConversationMessage extends Omit<ConversationMessageData, 'actor'> {
  account: mastodon.v1.Account
  state: 'pending' | 'failed' | 'delivered' | 'viewed'
}
