interface AuthInfo {
  apiUrl: string
  authorizationToken: string
}

interface StorageInfo extends AuthInfo {
  recommendedPartSize: number
  absoluteMinimumPartSize: number
}

interface AuthResponse {
  apiInfo: {
    storageApi: {
      apiUrl: string
      recommendedPartSize: number
      absoluteMinimumPartSize: number
    }
  }
  authorizationToken: string
}

interface GetUploadUrlResponse {
  bucketId: string
  uploadUrl: string
  authorizationToken: string
}

interface UploadResponse {
  fileName: string
  fileId: string
}

interface StartUploadResponse {
  fileId: string
}

interface GetUploadPartUrlResponse {
  fileId: string
  uploadUrl: string
  authorizationToken: string
}

async function got<T>(fn: Promise<Response>): Promise<T> {
  const res = await fn
  if (!res.ok) {
    const body = await res.text()
    const message = `Backblaze [${res.url}] returned [${res.status}]: ${body}`
    console.error(message)
    throw new Error(message)
  }
  return await res.json<T>()
}

export class Backblaze {
  private readonly authCommand: AuthCommand

  constructor(applicationKeyId: string, applicationKey: string) {
    this.authCommand = new AuthCommand(applicationKeyId, applicationKey)
  }

  async upload(bucketId: string, { file, name, size, body }: { file?: File, name?: string, size?: number, body?: BodyInit }) {
    const uploadCommand = new UploadCommand(new GetUploadUrlCommand(this.authCommand))
    return uploadCommand.upload(bucketId, name ?? file?.name, size ?? file?.size, body ?? file?.stream())
  }

  async delete(name: string) {
    const command = new DeleteFileCommand(this.authCommand)
    return command.delete(name)
  }

  async startUpload(bucketId: string, { name, size }: { name: string, size: number }) {
    const { recommendedPartSize: chunkSize, absoluteMinimumPartSize, ...auth } = await this.authCommand.get()
    if (size > absoluteMinimumPartSize) {
      const parts = Array(Math.ceil(size / chunkSize)).fill(0).map((_, i) => i).map((partNumber) => {
        const start = partNumber * chunkSize
        const end = start + chunkSize
        return {
          partNumber: partNumber + 1,
          start,
          end: end > size ? size : end,
        }
      })
      const { fileId } = await this.startLargeFile(auth, bucketId, name)
      const { uploadUrl, authorizationToken } = await this.getUploadPartUrl(auth, fileId)
      return {
        fileId,
        parts,
        uploadUrl,
        authorizationToken,
      }
    }
    const { uploadUrl, authorizationToken } = await this.getUploadUrl(auth, bucketId)
    return {
      uploadUrl,
      authorizationToken,
    }
  }

  async finishLargeFile(fileId: string, partSha1Array: string[]) {
    const { apiUrl, authorizationToken } = await this.authCommand.get()
    return got<StartUploadResponse>(fetch(`${apiUrl}/b2api/v3/b2_finish_large_file`, {
      headers: {
        Authorization: authorizationToken,
      },
      method: 'POST',
      body: JSON.stringify({ fileId, partSha1Array }),
    }))
  }

  private getUploadUrl({ apiUrl, authorizationToken }: AuthInfo, bucketId: string) {
    return got<GetUploadPartUrlResponse>(fetch(`${apiUrl}/b2api/v3/b2_get_upload_url?bucketId=${bucketId}`, {
      headers: {
        Authorization: authorizationToken,
      },
    }))
  }

  private startLargeFile({ apiUrl, authorizationToken }: AuthInfo, bucketId: string, fileName: string) {
    return got<StartUploadResponse>(fetch(`${apiUrl}/b2api/v3/b2_start_large_file`, {
      headers: {
        Authorization: authorizationToken,
      },
      method: 'POST',
      body: JSON.stringify({ bucketId, fileName, contentType: 'b2/x-auto' }),
    }))
  }

  private getUploadPartUrl({ apiUrl, authorizationToken }: AuthInfo, fileId: string) {
    return got<GetUploadPartUrlResponse>(fetch(`${apiUrl}/b2api/v3/b2_get_upload_part_url?fileId=${fileId}`, {
      headers: {
        Authorization: authorizationToken,
      },
    }))
  }
}

class AuthCommand {
  private res?: StorageInfo

  constructor(private readonly applicationKeyId: string, private readonly applicationKey: string) {
  }

  async get() {
    if (this.res === undefined) {
      const { apiInfo: { storageApi }, authorizationToken } = await got<AuthResponse>(fetch('https://api.backblazeb2.com/b2api/v4/b2_authorize_account', {
        headers: {
          Authorization: 'Basic ' + btoa(`${this.applicationKeyId}:${this.applicationKey}`),
        },
      }))
      this.res = {
        authorizationToken,
        apiUrl: storageApi.apiUrl,
        recommendedPartSize: storageApi.recommendedPartSize,
        absoluteMinimumPartSize: storageApi.absoluteMinimumPartSize,
      }
    }
    return this.res
  }
}

class GetUploadUrlCommand {
  private res?: GetUploadUrlResponse
  private ttl = 0

  constructor(private readonly authCommand: AuthCommand) {
  }

  async get(bucketId: string): Promise<GetUploadUrlResponse> {
    const { apiUrl, authorizationToken } = await this.authCommand.get()
    if (this.res === undefined || this.ttl < Date.now()) {
      this.res = await got<GetUploadUrlResponse>(fetch(`${apiUrl}/b2api/v4/b2_get_upload_url`, {
        headers: {
          Authorization: authorizationToken,
        },
        method: 'POST',
        body: JSON.stringify({ bucketId }),
      }))
      this.ttl = Date.now() + 24 * 60 * 60 * 1000
    }
    return this.res
  }
}

class UploadCommand {
  constructor(private readonly urlCommand: GetUploadUrlCommand) {
  }

  async upload(bucketId: string, name?: string, size?: number, body?: BodyInit): Promise<string> {
    const { uploadUrl, authorizationToken } = await this.urlCommand.get(bucketId)
    const { fileName } = await got<UploadResponse>(fetch(uploadUrl, {
      headers: {
        'Authorization': authorizationToken,
        'Content-Length': `${size ?? 0}`,
        'Content-Type': 'b2/x-auto',
        'X-Bz-File-Name': name ?? '',
        'X-Bz-Content-Sha1': 'do_not_verify',
      },
      method: 'POST',
      body,
    }))
    return fileName
  }
}

class DeleteFileCommand {
  constructor(private readonly authCommand: AuthCommand) {
  }

  async delete(fileName: string) {
    const { apiUrl, authorizationToken } = await this.authCommand.get()
    return await got<UploadResponse>(fetch(`${apiUrl}/b2api/v3/b2_delete_file_version`, {
      headers: {
        Authorization: authorizationToken,
      },
      method: 'POST',
      body: JSON.stringify({ fileName }),
    }))
  }
}
