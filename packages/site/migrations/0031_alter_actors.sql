-- Migration number: 0031 	 2024-07-09T06:26:10.339Z

PRAGMA defer_foreign_keys = on;

CREATE TABLE temp_actors (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL DEFAULT 'Person',
  email TEXT,
  privkey BLOB,
  privkey_salt BLOB,
  pubkey TEXT,
  cdate DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW')),
  properties TEXT NOT NULL DEFAULT (json_object()),
  is_admin INTEGER
);

INSERT INTO temp_actors (id, type, email, privkey, privkey_salt, pubkey, cdate, properties, is_admin)
SELECT id, type, email, privkey, privkey_salt, pubkey, cdate, properties, is_admin
FROM actors;

DROP TABLE actors;

ALTER TABLE temp_actors RENAME TO actors;

PRAGMA defer_foreign_keys = off;
