-- Migration number: 0025 	 2024-04-23T14:42:37.072Z
DROP TABLE IF EXISTS actor_following_actors;

CREATE TABLE IF NOT EXISTS actor_following_actors (
  id TEXT PRIMARY KEY,
  actor_id TEXT NOT NULL,
  target_actor_id TEXT NOT NULL,
  target_actor_acct TEXT NOT NULL,
  state TEXT NOT NULL DEFAULT 'pending',
  published_date DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW')),
  properties TEXT NOT NULL DEFAULT (json_object()),
  cdate DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW')),

  UNIQUE(actor_id, target_actor_id),
  FOREIG<PERSON> KEY(actor_id)  REFERENCES actors(id),
  FOREIGN KEY(target_actor_id) REFERENCES actors(id)
);

CREATE INDEX IF NOT EXISTS actor_following_actors_published_date ON actor_following_actors(published_date);
