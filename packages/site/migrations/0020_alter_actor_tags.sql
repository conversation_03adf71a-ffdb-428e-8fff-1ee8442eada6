-- Migration number: 0020 	 2024-04-22T18:37:53.001Z

DROP TABLE IF EXISTS actor_tags;
CREATE TABLE IF NOT EXISTS actor_following_tags (
  id TEXT PRIMARY KEY,
  actor_id TEXT NOT NULL,
  tag_id TEXT NOT NULL,
  published_date DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW')),
  properties TEXT NOT NULL DEFAULT (json_object()),
  cdate DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW')),

  UNIQUE(actor_id, tag_id),
  FOREIGN KEY(actor_id)  REFERENCES actors(id),
  FOREIGN KEY(tag_id) REFERENCES tags(id)
);

CREATE INDEX IF NOT EXISTS actor_following_tags_published_date ON actor_following_tags(published_date);
