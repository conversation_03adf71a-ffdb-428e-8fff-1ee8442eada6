-- Migration number: 0033 	 2024-07-22T05:59:48.930Z

CREATE VIRTUAL TABLE IF NOT EXISTS actors_search_fts USING fts5 (
    type UNINDEXED,
    name,
    username,
    summary
);

DROP TRIGGER IF EXISTS actors_search_fts_insert;
CREATE TRIGGER IF NOT EXISTS actors_search_fts_insert AFTER INSERT ON actors
BEGIN
    INSERT INTO actors_search_fts (rowid, type, name, username, summary)
    VALUES (new.rowid,
            new.type,
            json_extract(new.properties, '$.name'),
            json_extract(new.properties, '$.preferredUsername'),
            json_extract(new.properties, '$.summary'));
END;

DROP TRIGGER IF EXISTS actors_search_fts_update;
CREATE TRIGGER IF NOT EXISTS actors_search_fts_update AFTER UPDATE ON actors
BEGIN
    DELETE FROM actors_search_fts WHERE rowid=old.rowid;
    INSERT INTO actors_search_fts (rowid, type, name, username, summary)
    VALUES (new.rowid,
            new.type,
            json_extract(new.properties, '$.name'),
            json_extract(new.properties, '$.preferredUsername'),
            json_extract(new.properties, '$.summary'));
END;

-- INSERT INTO actors_search_fts (rowid, type, name, username, summary) SELECT rowid, type, json_extract(properties, '$.name'), json_extract(properties, '$.preferredUsername'), json_extract(properties, '$.summary') FROM actors
