-- Migration number: 0012 	 2023-12-07T09:52:27.152Z

CREATE TRIGGER IF NOT EXISTS objects_search_fts_insert AFTER INSERT ON objects
BEGIN
    INSERT INTO search_fts (rowid, type, status)
    VALUES (new.rowid, new.type, json_extract(new.properties, '$.text'));
END;

CREATE TRIGGER IF NOT EXISTS objects_search_fts_update AFTER UPDATE OF properties ON objects
BEGIN
    DELETE FROM search_fts WHERE rowid=old.rowid;
    INSERT INTO search_fts (rowid, type, status)
    VALUES (new.rowid, new.type, json_extract(new.properties, '$.text'));
END;

CREATE TRIGGER IF NOT EXISTS objects_search_fts_delete AFTER DELETE ON objects
BEGIN
    DELETE FROM search_fts WHERE rowid=old.rowid;
END;
