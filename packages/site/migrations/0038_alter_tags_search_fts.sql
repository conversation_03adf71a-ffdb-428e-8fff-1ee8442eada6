-- Migration number: 0038 	 2024-07-22T05:59:48.930Z

DROP TRIGGER IF EXISTS tags_search_fts_insert;
CREATE TRIGGER IF NOT EXISTS tags_search_fts_insert AFTER INSERT ON tags
BEGIN
    INSERT INTO tags_search_fts (rowid, display_name)
    VALUES (new.rowid, new.display_name);
END;

DROP TRIGGER IF EXISTS tags_search_fts_update;
CREATE TRIGGER IF NOT EXISTS tags_search_fts_delete AFTER DELETE ON tags
BEGIN
    DELETE FROM tags_search_fts WHERE rowid=old.rowid;
END;

DROP TRIGGER IF EXISTS tags_search_fts_delete;
CREATE TRIGGER IF NOT EXISTS tags_search_fts_update AFTER UPDATE ON tags
BEGIN
    DELETE FROM tags_search_fts WHERE rowid=old.rowid;
    INSERT INTO tags_search_fts (rowid, display_name)
    VALUES (new.rowid, new.display_name);
END;

-- INSERT INTO tags_search_fts (rowid, display_name) SELECT rowid, display_name FROM tags
