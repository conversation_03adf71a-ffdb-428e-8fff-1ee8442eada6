-- Migration number: 0029 	 2024-05-16T17:08:15.206Z

DROP TABLE IF EXISTS search_fts;
CREATE VIRTUAL TABLE IF NOT EXISTS search_fts USING fts5 (
    type,
    actor_name,
    actor_username,
    actor_summary,
    status,
    tag_name
);

-- DELETE FROM search_fts
-- INSERT INTO search_fts (rowid, type, actor_name, actor_username, actor_summary) SELECT type || rowid, type, json_extract(properties, '$.name'), json_extract(properties, '$.preferredUsername'), json_extract(properties, '$.summary') FROM actors
-- INSERT INTO search_fts (rowid, type, tag_name) SELECT rowid, 'tag', display_name FROM tags
-- INSERT INTO search_fts (rowid, type, tag_name) SELECT rowid, type, json_extract(properties, '$.text') FROM objects

DROP TRIGGER IF EXISTS actors_search_fts_insert;
CREATE TRIGGER IF NOT EXISTS actors_search_fts_insert AFTER INSERT ON actors
BEGIN
    INSERT INTO search_fts (rowid, type, actor_name, actor_username, actor_summary)
    VALUES (new.rowid,
            new.type,
            json_extract(new.properties, '$.name'),
            json_extract(new.properties, '$.preferredUsername'),
            json_extract(new.properties, '$.summary'));
END;

DROP TRIGGER IF EXISTS actors_search_fts_update;
CREATE TRIGGER IF NOT EXISTS actors_search_fts_update AFTER UPDATE ON actors
BEGIN
    DELETE FROM search_fts WHERE rowid=old.rowid;
    INSERT INTO search_fts (rowid, type, actor_name, actor_username, actor_summary)
    VALUES (new.rowid,
            new.type,
            json_extract(new.properties, '$.name'),
            json_extract(new.properties, '$.preferredUsername'),
            json_extract(new.properties, '$.summary'));
END;


DROP TRIGGER IF EXISTS tags_search_fts_insert;
CREATE TRIGGER IF NOT EXISTS tags_search_fts_insert AFTER INSERT ON tags
BEGIN
    INSERT INTO search_fts (rowid, type, tag_name)
    VALUES (new.rowid, 'tag', new.display_name);
END;

DROP TRIGGER IF EXISTS tags_search_fts_update;
CREATE TRIGGER IF NOT EXISTS tags_search_fts_update AFTER UPDATE ON tags
BEGIN
    DELETE FROM search_fts WHERE rowid=old.rowid;
    INSERT INTO search_fts (rowid, type, tag_name)
    VALUES (new.rowid, 'tag', new.display_name);
END;
