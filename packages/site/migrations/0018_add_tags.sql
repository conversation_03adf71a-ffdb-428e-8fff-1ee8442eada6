-- Migration number: 0018 	 2024-03-21T09:59:53.203Z
-- (lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6))))

CREATE TABLE IF NOT EXISTS tags (
  id TEXT PRIMARY KEY,
  display_name TEXT,
  cdate DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW'))
);

CREATE TABLE IF NOT EXISTS note_tags (
  object_id TEXT NOT NULL,
  tag_id TEXT NOT NULL,
  cdate DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW')),

  UNIQUE(object_id, tag_id),
  FOREIGN KEY(object_id) REFERENCES objects(id),
  FOREIGN KEY(tag_id) REFERENCES tags(id)
);
