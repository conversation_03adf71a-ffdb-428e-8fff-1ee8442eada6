-- Migration number: 0030 	 2024-06-16T12:33:56.220Z

DROP TABLE IF EXISTS actor_conversation_objects;
CREATE TABLE IF NOT EXISTS actor_conversation_objects (
  id TEXT PRIMARY KEY,
  actor_id TEXT NOT NULL,
  conversation_id TEXT NOT NULL,
  published_date DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW')),
  properties TEXT NOT NULL DEFAULT (json_object()),
  cdate DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW')),

  FOREIGN KEY(actor_id)  REFERENCES actors(id),
  FOREIGN KEY(conversation_id) REFERENCES conversations(id)
);

CREATE INDEX IF NOT EXISTS actor_conversation_objects_published_date ON actor_conversation_objects(published_date);
