-- Migration number: 0011 	 2023-11-30T14:30:29.860Z

CREATE TABLE IF NOT EXISTS polls (
  id TEXT PRIMARY KEY,
  object_id TEXT NOT NULL,
  cdate DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW')),

  FOREIGN KEY(object_id) REFERENCES objects(id)
);

CREATE TABLE IF NOT EXISTS poll_votes (
  actor_id TEXT NOT NULL,
  poll_id TEXT NOT NULL,
  choice INTEGER NOT NULL,
  cdate DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW')),

  UNIQUE(actor_id, poll_id, choice),
  FOREIGN KEY(actor_id)  REFERENCES actors(id),
  FOREIG<PERSON> KEY(poll_id) REFERENCES polls(rowid)
);
