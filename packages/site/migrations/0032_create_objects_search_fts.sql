-- Migration number: 0032 	 2024-07-22T05:59:48.930Z

CREATE VIRTUAL TABLE IF NOT EXISTS objects_search_fts USING fts5 (
    type UNINDEXED,
    content
);

CREATE TRIGGER IF NOT EXISTS objects_search_fts_insert AFTER INSERT ON objects
BEGIN
    INSERT INTO objects_search_fts (rowid, type, content)
    VALUES (new.rowid, new.type, json_extract(new.properties, '$.text'));
END;

CREATE TRIGGER IF NOT EXISTS objects_search_fts_update AFTER UPDATE OF properties ON objects
BEGIN
    DELETE FROM objects_search_fts WHERE rowid=old.rowid;
    INSERT INTO objects_search_fts (rowid, type, content)
    VALUES (new.rowid, new.type, json_extract(new.properties, '$.text'));
END;

CREATE TRIGGER IF NOT EXISTS objects_search_fts_delete AFTER DELETE ON objects
BEGIN
    DELETE FROM objects_search_fts WHERE rowid=old.rowid;
END;

-- INSERT INTO objects_search_fts (rowid, type, content) SELECT rowid, type, json_extract(properties, '$.text') FROM objects
