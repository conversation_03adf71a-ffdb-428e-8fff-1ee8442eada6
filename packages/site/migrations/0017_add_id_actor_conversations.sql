-- Migration number: 0017 	 2024-01-12T14:55:48.910Z

DROP TABLE IF EXISTS actor_conversations;
CREATE TABLE IF NOT EXISTS actor_conversations (
  id TEXT PRIMARY KEY,
  actor_id TEXT NOT NULL,
  conversation_id TEXT NOT NULL,
  properties TEXT NOT NULL DEFAULT (json_object()),
  cdate DATETIME NOT NULL DEFAULT (STRFTIME('%Y-%m-%d %H:%M:%f', 'NOW')),

  UNIQUE(conversation_id, actor_id),
  FOREIGN KEY(actor_id) REFERENCES actors(id),
  FOREIG<PERSON> KEY(conversation_id) REFERENCES conversations(id)
);
