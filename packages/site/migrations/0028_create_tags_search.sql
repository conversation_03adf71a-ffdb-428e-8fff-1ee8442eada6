-- Migration number: 0028 	 2024-05-14T14:24:22.619Z

CREATE TRIGGER IF NOT EXISTS tags_search_fts_insert AFTER INSERT ON tags
BEGIN
    INSERT INTO search_fts (rowid, type, display_name)
    VALUES (new.rowid, 'tag', new.display_name);
END;

CREATE TRIGGER IF NOT EXISTS tags_search_fts_delete AFTER DELETE ON tags
BEGIN
    DELETE FROM search_fts WHERE rowid=old.rowid;
END;

CREATE TRIGGER IF NOT EXISTS tags_search_fts_update AFTER UPDATE ON tags
BEGIN
    DELETE FROM search_fts WHERE rowid=old.rowid;
    INSERT INTO search_fts (rowid, type, display_name)
    VALUES (new.rowid, 'tag', new.display_name);
END;
