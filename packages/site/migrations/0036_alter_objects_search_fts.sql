-- Migration number: 0036 	 2024-07-22T05:59:48.930Z

DROP TRIGGER IF EXISTS objects_search_fts_insert;
CREATE TRIGGER IF NOT EXISTS objects_search_fts_insert AFTER INSERT ON objects
BEGIN
    INSERT INTO objects_search_fts (rowid, type, content)
    VALUES (new.rowid, new.type, json_extract(new.properties, '$.text'));
END;

DROP TRIGGER IF EXISTS objects_search_fts_update;
CREATE TRIGGER IF NOT EXISTS objects_search_fts_update AFTER UPDATE OF properties ON objects
BEGIN
    DELETE FROM objects_search_fts WHERE rowid=old.rowid;
    INSERT INTO objects_search_fts (rowid, type, content)
    VALUES (new.rowid, new.type, json_extract(new.properties, '$.text'));
END;

DROP TRIGGER IF EXISTS objects_search_fts_delete;
CREATE TRIGGER IF NOT EXISTS objects_search_fts_delete AFTER DELETE ON objects
BEGIN
    DELETE FROM objects_search_fts WHERE rowid=old.rowid;
END;

-- INSERT INTO objects_search_fts (rowid, type, content) SELECT rowid, type, json_extract(properties, '$.text') FROM objects
