{"name": "lib-worker", "private": true, "type": "module", "main": "./dist/index.umd.cjs", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.umd.cjs"}}, "types": "./dist/index.d.ts", "scripts": {"clean": "rm -rf node_modules package-lock.json dist", "build": "rm -rf dist && run-p build:*", "build:ts-types": "tsc --emitDeclarationOnly --outDir dist", "build:ems": "esbuild src/index.ts --bundle --outdir=dist --sourcemap --minify --splitting --format=esm --target=esnext", "build:umd": "esbuild src/index.ts --bundle --outfile=dist/index.umd.cjs --sourcemap --minify --platform=node --target=node10.4"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241112.0", "npm-run-all": "^4.1.5", "typescript": "^5.6.3"}}