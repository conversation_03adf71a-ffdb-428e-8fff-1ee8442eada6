export interface HTTPError extends Error {
  readonly status?: number
  readonly contentType?: string
  readonly code?: string
}

export class HTTPError extends Error {

  constructor(message: string, public readonly status?: HTTPError['status'], public readonly contentType?: HTTPError['contentType']) {
    super(message)
  }

  static throw(status: number, message = ''): never {
    throw new this(message, status)
  }
}

export function errorHandler(err: HTTPError) {
  console.error(err.stack)
  return new Response(err.message || 'Server Error', {
    status: err.status || 500,
    headers: {
      'content-type': err.contentType || 'text/plain',
    },
  })
}
