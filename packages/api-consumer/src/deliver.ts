import type { DeliverMessageBody } from 'types/queue'
import { getSigningKey } from 'backend/mastodon/account'
import * as actors from 'backend/activitypub/actors'
import type { Actor } from 'backend/activitypub/actors'
import type { Env } from './'
import { deliverToActor } from 'backend/activitypub/deliver'

export async function handleDeliverMessage(env: Env, actor: Actor, message: DeliverMessageBody) {
	const toActorId = new URL(message.toActorId)
	const targetActor = await actors.getAndCache(toActorId, env.DATABASE)
	if (targetActor === null) {
		console.warn(`actor ${toActorId} not found`)
		return
	}

	const signingKey = await getSigningKey(message.userKEK, env.DATABASE, actor)
	await deliverToActor(signing<PERSON><PERSON>, actor, targetActor, message.activity, env.DOMAIN)
}
