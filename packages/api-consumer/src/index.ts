import type { MessageBody, InboxMessageBody, DeliverMessageBody } from 'types/queue'
import * as actors from 'backend/activitypub/actors'
import { MessageType } from 'types/queue'
import { handleInboxMessage } from './inbox'
import { handleDeliverMessage } from './deliver'

export type Env = {
  DATABASE: D1Database
  DOMAIN: string
  ADMIN_EMAIL: string
  DO_CACHE: DurableObjectNamespace
}

const worker: ExportedHandler<Env, MessageBody> = {
  async queue(batch, env, ctx) {
    const db = env.DATABASE

    try {
      for (const message of batch.messages) {
        const actor = await actors.getActorById(db, new URL(message.body.actorId))
        if (actor === null) {
          console.warn(`actor ${message.body.actorId} is missing`)
          return
        }

        switch (message.body.type) {
          case MessageType.Inbox: {
            await handleInboxMessage(env, ctx, actor, message.body as InboxMessageBody)
            break
          }
          case MessageType.Deliver: {
            await handleDeliverMessage(env, actor, message.body as DeliverMessageBody)
            break
          }
          default:
            throw new Error('unsupported message type: ' + message.body.type)
        }
      }
    } catch (err: any) {
      console.error(err.stack, err.cause)
    }
  },
}

export default worker
