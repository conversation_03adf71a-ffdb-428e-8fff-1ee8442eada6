import { Hono, type Env as HonoEnv } from 'hono'
import { presets } from './image-kit'
import { Backblaze } from './backblaze'
import upload from './upload'

function clearHeaders(headers: Headers) {
  const UNSIGNABLE_HEADERS = [
    'x-forwarded-proto',
    'x-real-ip',
    // We can't include accept-encoding in the signature because Cloudflare
    // sets the incoming accept-encoding header to "gzip, br", then modifies
    // the outgoing request to set accept-encoding to "gzip".
    // Not cool, Cloudflare!
    'accept-encoding'
  ]
  return Array.from(headers.entries())
    .filter(([name]) => !UNSIGNABLE_HEADERS.includes(name))
    .filter(([name]) => !name.startsWith('cf-'))
}

function isObjectBody(obj: R2Object | R2ObjectBody): obj is R2ObjectBody {
  return 'body' in obj ? true : false
}

type AppEnv = HonoEnv & {
  Bindings: Env
}

const app = new Hono<AppEnv>().basePath('/media')

app.on(['GET', 'HEAD'], '/base/:name', async c => {
  const res = await c.env.BUCKET.get(c.req.param('name'), {
    range: c.req.raw.headers,
    onlyIf: c.req.raw.headers
  })
  if (res === null) {
    return c.notFound()
  }

  const headers = new Headers()
  res.writeHttpMetadata(headers)
  headers.set('etag', res.httpEtag)
  headers.set('accept-ranges', 'bytes')
  if (res.range) {
    const range = res.range as any;
    headers.set('content-range', `bytes ${range.offset}-${range.end ?? res.size - 1}/${res.size}`)
  }
  if (isObjectBody(res)) {
    const status = c.req.raw.headers.get('range') !== null ? 206 : 200;
    return new Response(res.body, {
      status,
      headers
    })
  } else {
    return new Response(undefined, {
      status: 304,
      headers
    })
  }
})

app.on(['GET', 'HEAD'], '/original/:name', c => {
  const client = new Backblaze(c.env.BACKBLAZE_ENDPOINT, {
    accessKeyId: c.env.BACKBLAZE_KEY_ID,
    secretAccessKey: c.env.BACKBLAZE_KEY,
    service: 's3',
    region: c.env.BACKBLAZE_REGION
  })
  return client.got(c.req.method, clearHeaders(c.req.raw.headers), c.req.param('name'))
})

app.on(['GET', 'HEAD'], '/:preset/:name', c => {
  const transform = presets[c.req.param('preset')]
  if (transform === undefined) {
    return c.notFound()
  }
  return fetch(`${c.env.IMAGEKIT_ENDPOINT}/${transform}/${c.req.param('name')}`, {
    method: c.req.method,
    headers: clearHeaders(c.req.raw.headers)
  })
})

app.route('/upload', upload)

export default app
