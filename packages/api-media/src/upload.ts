import { Hono, type Env as HonoEnv } from 'hono'
import { Backblaze } from '~/backend/media/backblaze'

type AppEnv = HonoEnv & {
  Bindings: Env
}
const r = new Hono<AppEnv>()

r.post('/create/:name', async (c) => {
  const res = await c.env.BUCKET.createMultipartUpload(c.req.param('name'))
  return c.json(res)
})

r.put('/:name/:uploadId/:part', async (c) => {
  if (c.req.raw.body === null) {
    return c.text('missing_body', 400)
  }
  const { name, uploadId, part } = c.req.param()
  const multipart = c.env.BUCKET.resumeMultipartUpload(name, uploadId)
  const res = await multipart.uploadPart(parseInt(part), c.req.raw.body)
  return c.json(res)
})

r.put('/:name', async (c) => {
  if (c.req.raw.body === null) {
    return c.text('missing_body', 400)
  }
  const domain = new URL(c.req.url).hostname
  const { name } = c.req.param()
  await c.env.BUCKET.put(name, c.req.raw.body)

  c.executionCtx.waitUntil(c.env.BUCKET.delete(name))
  const object = await c.env.BUCKET.get(name)
  if (object) {
    const backblaze = new Backblaze(c.env.BACKBLAZE_KEY_ID, c.env.BACKBLAZE_KEY)
    const fileName = await backblaze.upload(c.env.BACKBLAZE_BUCKET_ID, { name, size: object.size, body: object.body })
    return c.json({ url: `https://${domain}/media/original/${fileName}` })
  }
  return c.notFound()
})

r.post('/finish/:name/:uploadId', async (c) => {
  if (c.req.raw.body === null) {
    return c.text('missing_body', 400)
  }
  const domain = new URL(c.req.url).hostname
  const { name, uploadId } = c.req.param()
  const multipart = c.env.BUCKET.resumeMultipartUpload(name, uploadId)
  const body = await c.req.json<{ parts: R2UploadedPart[] }>()
  console.log(body)
  await multipart.complete(body.parts)

  // c.executionCtx.waitUntil(c.env.BUCKET.delete(name))
  const object = await c.env.BUCKET.get(name)
  if (object) {
    // const backblaze = new Backblaze(c.env.BACKBLAZE_KEY_ID, c.env.BACKBLAZE_KEY)
    // const fileName = await backblaze.upload(c.env.BACKBLAZE_BUCKET_ID, { name, size: object.size, body: object.body })
    return c.json({ url: `https://s.${domain}/${name}` })
  }
  return c.notFound()
})

export default r
