{"name": "media", "version": "0.0.0", "private": true, "type": "module", "scripts": {"vars": "echo BACKBLAZE_KEY=$BACKBLAZE_KEY > .dev.vars", "clean": "rm -rf node_modules package-lock.json .wrangler", "dev": "wrangler dev", "prepublish:vars": "echo $BACKBLAZE_KEY | wrangler secret put BACKBLAZE_KEY", "prepublish": "npm-run-all --parallel prepublish:*", "publish": "wrangler deploy --minify"}, "dependencies": {"aws4fetch": "^1.0.20", "hono": "^4.6.9"}, "devDependencies": {"dev-worker": "*"}}