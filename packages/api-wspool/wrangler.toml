name = "wspool"
compatibility_date = "2023-03-14"
workers_dev = false

main = "src/index.ts"

routes = [
	{ pattern = "*individuallist.xyz/wspool/*", zone_name = "individuallist.xyz" }
]

d1_databases = [
  { binding = "DB", database_name = "ws", database_id = "245eed12-bdaa-47ed-94a5-ec18753a059d", preview_database_id = "245eed12-bdaa-47ed-94a5-ec18753a059d" }
]

[durable_objects]
bindings = [
  {name = "POOL", class_name = "WsConnectionPool"},
]

[[migrations]]
new_classes = ["WsConnectionPool"]
tag = "v1"
