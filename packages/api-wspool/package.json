{"name": "wspool", "version": "0.0.0", "private": true, "type": "module", "scripts": {"clean": "rm -rf node_modules package-lock.json .wrangler", "predev": "npm run db:migrations", "dev": "wrangler dev", "logs": "wrangler tail", "db:create-migrations": "wrangler d1 migrations create DB", "db:migrations": "wrangler d1 migrations apply DB --local", "prepublish:db": "wrangler d1 migrations apply DB --remote", "prepublish": "npm-run-all --parallel prepublish:*", "publish": "wrangler deploy"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241112.0", "typescript": "^5.6.3", "wrangler": "^3.86.1"}, "dependencies": {"@hono/zod-validator": "^0.2.2", "hono": "^4.6.9", "radash": "^12.1.0"}}