// https://github.com/bubblydoo/graphql-workers-subscriptions/blob/main/src/wsConnectionPool.ts

import { Hono, type Env as HonoEnv } from 'hono'
import { Subscriptions } from '~/libs/subscriptions'

type AppEnv = HonoEnv & {
  Bindings: Env
}

export class WsConnectionPool implements DurableObject {
  private readonly subscriptions: Subscriptions
  private readonly app = new Hono<AppEnv>()

  constructor(private readonly state: DurableObjectState, private readonly env: Env) {
    this.subscriptions = new Subscriptions(env.DB)

    this.state.setWebSocketAutoResponse(new WebSocketRequestResponsePair('ping', 'pong'))

    this.app.get('/connect/:id', c => {
      const connectionId = c.req.param('id')
      console.log('connect', { connectionId })

      const [webSocket, connection] = Object.values(new WebSocketPair())
      connection.serializeAttachment({ connectionId })

      // accept socket to begin
      this.state.acceptWebSocket(connection, [connectionId])

      return new Response(null, { status: 101, webSocket })
    })

    this.app.get('/close/:id', c => {
      const connectionId = c.req.param('id')
      const connections = this.state.getWebSockets(connectionId)
      if (connections.length) {
        for (const connection of connections) {
          connection.close()
        }
      } else {
        c.executionCtx.waitUntil(this.subscriptions.deleteBy({ connectionId }))
      }
      return c.text('')
    })

    this.app.post('/publish', async c => {
      const messages = await c.req.json<PublishMessage[]>()
      for (const { connectionId, payload } of messages) {
        const connections = this.state.getWebSockets(connectionId)
        if (connections.length === 0 || connections.every(({ readyState }) => readyState === WebSocket.READY_STATE_CLOSED)) {
          await this.subscriptions.deleteBy({ connectionId })
        } else {
          for (const connection of connections) {
            connection.send(JSON.stringify(payload))
          }
        }
      }
      return c.text('')
    })
  }

  async webSocketMessage(ws: WebSocket, data: string) {
    console.log('message', { ws, data })
    let json: any
    try {
      json = JSON.parse(data)
    } catch (e) {
      // no-op
    }
    try {
      if (typeof json === 'object') {
        const { type, channel } = json
        if (type === 'subscribe' && channel) {
          const { connectionId } = ws.deserializeAttachment()
          await this.subscriptions.insert({
            connectionPoolId: this.state.id.toString(),
            connectionId,
            channel
          })
        } else if (type === 'complete') {
          const { connectionId } = ws.deserializeAttachment()
          await this.subscriptions.deleteBy({ connectionId, channel })
        }
      }
    } catch (e) {
      console.warn(e)
    }
  }

  async webSocketClose(ws: WebSocket, code: number, reason: string, wasClean: boolean) {
    console.log('closed', { ws, code, reason, wasClean })
    await this.failure(ws)
  }

  async webSocketError(ws: WebSocket, error: any) {
    console.error('error', { ws, error })
    await this.failure(ws)
  }

  async fetch(request: Request) {
    return this.app.fetch(request, this.env)
  }

  private async failure(ws: WebSocket) {
    const { connectionId } = ws.deserializeAttachment()
    await this.subscriptions.deleteBy({ connectionId })
  }
}
