import { zValidator } from '@hono/zod-validator'
import { Hono, type Env as HonoEnv } from 'hono'
import { group } from 'radash'
import { z } from 'zod'
import { Subscriptions } from '~/libs/subscriptions'
import { home } from './home'

type AppEnv = HonoEnv & {
  Bindings: Env
}

const app = new Hono<AppEnv>().basePath('/wspool')

app.get('/', c => c.html(home))

app.get('/connect', async c => {
  if (c.req.header('Upgrade') !== 'websocket') {
    return c.text('Invalid Upgrade header', { status: 400 })
  }
  const colo = c.req.raw.cf?.colo as string
  const continent = c.req.raw.cf?.continent as string
  let stubName = 'global'
  if (c.env.STRATEGY === 'none') {
    stubName = crypto.randomUUID()
  }
  if (c.env.STRATEGY === 'colo' && colo) {
    stubName = colo
  }
  if (c.env.STRATEGY === 'continent' && continent) {
    stubName = continent
  }
  const stubId = c.env.POOL.idFromName(stubName)
  const stub = c.env.POOL.get(stubId)
  return stub.fetch(`https://ws-do.internal/connect/${crypto.randomUUID()}`, c.req.raw)
})

app.post('/publish',
  zValidator('json', z.object({
    channel: z.string(),
    payload: z.any()
  })),
  async (c) => {
    const payload = c.req.valid('json')
    const publish = async () => {
      const subscriptions = await new Subscriptions(c.env.DB).query(payload.channel)
      // group subscriptions by connection pool
      const connectionPool = group(subscriptions, ({ connectionPoolId }) => connectionPoolId)
      return Promise.all(Object.entries(connectionPool).map(([connectionPoolId, subs]) => {
        if (subs?.length) {
          const stubId = c.env.POOL.idFromString(connectionPoolId)
          return c.env.POOL.get(stubId).fetch(`https://ws-do.internal/publish`, {
            method: 'POST',
            headers: { 'content-type': 'application/json' },
            body: JSON.stringify(subs.map<PublishMessage>(({ connectionId }) => ({ connectionId, payload })))
          })
        }
        return Promise.resolve()
      }))
    }
    c.executionCtx.waitUntil(publish())
    return c.text('')
  }
)

export default app
