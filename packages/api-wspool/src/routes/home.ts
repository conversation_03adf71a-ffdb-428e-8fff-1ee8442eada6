import { html } from 'hono/html'

export const home = html`
<!DOCTYPE html>
<html lang="en">
  <head>
    <title></title>
  </head>
  <body>
    <ul id="messages"></ul>
    <input type="text" id="account-id" placeholder="account_id" />
    <button id="go-subscribe">subscribe</button>
    <button id="go-complete">unsubscribe</button>
    <script>
      const accountInput = document.querySelector("#account-id")
      const subscribeButton = document.querySelector("#go-subscribe")
      const completeButton = document.querySelector("#go-complete")
      let socketA

      completeButton.disabled = true
      subscribeButton.addEventListener("click", event => {
        const accountId = accountInput.value
        if (accountId.length > 0) {
          socketA = new WebSocket('ws://' + window.location.host + '/wspool/connect')
          socketA.addEventListener('open', (event) => {
            event.currentTarget.disabled = true
            accountInput.disabled = true
            subscribeButton.disabled = true
            completeButton.disabled = false
            socketA.send(JSON.stringify({ type: 'subscribe', channel: 'timeline:direct:' + accountId }))
          })
          socketA.addEventListener('message', (event) => {
            console.log('message A ', event.data)
          })
        }
      });
      completeButton.addEventListener("click", event => {
        event.currentTarget.disabled = true
        const accountId = accountInput.value
        if (socketA && accountId.length > 0) {
          accountInput.disabled = false
          subscribeButton.disabled = false
          completeButton.disabled = true
          socketA.send(JSON.stringify({ type: 'complete', channel: 'timeline:direct:' + accountId }))
        }
      });
    </script>
  </body>
</html>
`
