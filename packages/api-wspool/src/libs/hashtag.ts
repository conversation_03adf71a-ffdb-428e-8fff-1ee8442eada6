/**
 * See app/lib/ascii_folder.rb for the canon definitions of these constants
 */
const NON_ASCII_CHARS = 'ÀÁÂÃÄÅàáâãäåĀāĂăĄąÇçĆćĈĉĊċČčÐðĎďĐđÈÉÊËèéêëĒēĔĕĖėĘęĚěĜĝĞğĠġĢģĤĥĦħÌÍÎÏìíîïĨĩĪīĬĭĮįİıĴĵĶķĸĹĺĻļĽľĿŀŁłÑñŃńŅņŇňŉŊŋÒÓÔÕÖØòóôõöøŌōŎŏŐőŔŕŖŗŘřŚśŜŝŞşŠšſŢţŤťŦŧÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųŴŵÝýÿŶŷŸŹźŻżŽž'
const EQUIVALENT_ASCII_CHARS = 'AAAAAAaaaaaaAaAaAaCcCcCcCcCcDdDdDdEEEEeeeeEeEeEeEeEeGgGgGgGgHhHhIIIIiiiiIiIiIiIiIiJjKkkLlLlLlLlLlNnNnNnNnnNnOOOOOOooooooOoOoOoRrRrRrSsSsSsSssTtTtTtUUUUuuuuUuUuUuUuUuUuWwYyyYyYZzZzZz'

/**
 * @param {string} str
 * @returns {string}
 */
export function normalizeHashtag(str: string): string {
  const regex = new RegExp(NON_ASCII_CHARS.split('').join('|'), 'g');
  return str
    .normalize('NFKC')
    .toLowerCase()
    .replace(regex, match => {
      const index = NON_ASCII_CHARS.indexOf(match)
      return EQUIVALENT_ASCII_CHARS[index]
    })
    .replace(/[^\p{L}\p{N}_\u00b7\u200c]/gu, '')
}
