interface WsSubscription {
  connectionPoolId: string
  connectionId: string
  channel: string
}

interface Row {
  connection_pool_id: string
  connection_id: string
  channel: string
}

function toSubscription(row: Row): WsSubscription {
  return {
    connectionId: row.connection_id,
    connectionPoolId: row.connection_pool_id,
    channel: row.channel,
  }
}

export class Subscriptions {
  constructor(private readonly db: D1Database) { }

  async query(channel: string) {
    const sql = 'SELECT * FROM ws_subscriptions WHERE channel = ?'
    console.debug(sql)
    console.debug([channel])
    const { results, success, error } = await this.db.prepare(sql).bind(channel).all<Row>()
    if (!success) {
      throw new Error('SQL error: ' + error)
    }
    return results?.map(toSubscription) ?? []
  }

  async insert({ connectionId, connectionPoolId, channel }: WsSubscription) {
    const binds = {
      connection_id: connectionId,
      connection_pool_id: connectionPoolId,
      channel,
    }
    const keys = Object.keys(binds)
    const sql = `INSERT INTO ws_subscriptions(${keys.join(',')}) VALUES(${Array(keys.length).fill('?')})`
    await this.db.prepare(sql).bind(...Object.values(binds)).run()
  }

  async deleteBy({ connectionId, channel }: { connectionId: string, channel?: string }) {
    const rules = ['connection_id = ?']
    const binds = [connectionId]
    if (channel) {
      rules.push('channel = ?')
      binds.push(channel)
    }
    const where = rules.join(' AND ')
    const query = `DELETE FROM ws_subscriptions WHERE ${where}`
    console.debug(query)
    console.debug(binds)
    await this.db.prepare(query).bind(...binds).run()
  }
}
