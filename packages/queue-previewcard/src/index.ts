import { Bindings, JsonResponse } from 'lib-worker'
import { parse } from 'ultrahtml'
import { querySelectorAll } from 'ultrahtml/selector'
import { getMastodonStatusById } from '~/backend/mastodon/status'
import { cacheFromEnv } from '~/backend/cache'
import { oembed } from '~/backend/microformats/oembed'

interface Env extends Bindings {
  DB: D1Database
  DOMAIN: string
  DO_CACHE: DurableObjectNamespace
}

interface RpcPayload {
  readonly url: string
}

function slient<T = any>(fn: () => T): T | undefined {
  try {
    return fn()
  } catch (err: any) {
    console.error(err.stack, err.cause)
  }
}

function parseUrls(content: string): string | undefined {
  return querySelectorAll(parse(content), 'a')
    // Avoid links for hashtags and mentions (microformats)
    .filter(({ attributes }) => !(attributes?.rel as string).includes('tag') && !(attributes?.class as string)?.match(/u-url|h-card/))
    // Avoid invalid URLs
    .map(node => slient<URL>(() => new URL(node.attributes?.href)))
    .filter((url): url is URL => Boolean(url))
    .filter(url => Boolean(url.host) && ['http', 'https'].includes(url.protocol))
    .map(url => url.href)
    .shift()
}

const worker: ExportedHandler<Env, string> = {
  async queue({ messages }, env, ctx) {
    try {
      for (const { body: id } of messages) {
        const status = await getMastodonStatusById(env.DB, id, env.DOMAIN)
        if (status) {
          const link = parseUrls(status.content)
          if (link) {
            await oembed(link, cacheFromEnv(env.DO_CACHE, ctx.waitUntil))
          }
        }
      }
    } catch (err: any) {
      console.error(err.stack, err.cause)
    }
  },

  async fetch(request, env, ctx) {
    if (request.method !== 'POST') {
      return new Response('Method Not Allowed', {
        status: 405,
        headers: {
          'content-type': 'text/plain',
        }
      })
    }
    const { url } = await request.json<RpcPayload>()
    if (!url) {
      return new Response('Bad Request', {
        status: 400,
        headers: {
          'content-type': 'text/plain',
        }
      })
    }
    const data = await oembed(url, cacheFromEnv(env.DO_CACHE, ctx.waitUntil))

    return new JsonResponse(data)
  }
}

export default worker
