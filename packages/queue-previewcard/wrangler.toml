# Development

name = "queue-previewcard"
main = "./src/index.ts"
compatibility_date = "2023-01-09"

[[queues.consumers]]
  queue = "previewcard-queue"
  max_batch_size = 10
  max_batch_timeout = 30
  max_retries = 10

[durable_objects]
bindings = [
  { name = "DO_CACHE", script_name="cache", class_name = "ObjectCache" }
]


[[d1_databases]]
binding = "DB"
database_name = "db"
database_id = "b69dc291-58a2-474d-9a92-f3c58cf03c38"
preview_database_id = "b69dc291-58a2-474d-9a92-f3c58cf03c38"

[vars]
DOMAIN = "individuallist.xyz"
