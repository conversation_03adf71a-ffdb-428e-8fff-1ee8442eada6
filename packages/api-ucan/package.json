{"name": "ucan", "version": "0.0.0", "private": true, "type": "module", "main": "src/index.ts", "scripts": {"vars": "echo '' > .dev.vars && echo NFT_STORAGE_API_TOKEN=$NFT_STORAGE_API_TOKEN >> .dev.vars && echo NFT_STORAGE_DID=$NFT_STORAGE_DID >> .dev.vars && echo UCAN_PRIVATE_KEY=$UCAN_PRIVATE_KEY >> .dev.vars", "clean": "rm -rf node_modules package-lock.json .wrangler", "dev": "wrangler dev", "publish": "wrangler deploy"}, "dependencies": {"lib-worker": "*", "ucan-storage": "^1.3.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241112.0", "typescript": "^5.6.3", "wrangler": "^3.86.1"}}