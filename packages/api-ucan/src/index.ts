import { Bindings, error<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JsonResponse } from 'lib-worker'
import { KeyPair } from 'ucan-storage/keypair'
import { build } from 'ucan-storage/ucan-storage'
import { registerDid, getServiceUcan } from './ucan-store'

interface Env extends Bindings {
  NFT_STORAGE_API_TOKEN: string
  NFT_STORAGE_DID: string
  UCAN: KVNamespace
  UCAN_PRIVATE_KEY: string
}

const worker: ExportedHandler<Env> = {
  async fetch(request, env) {
    try {
      const kp = await KeyPair.fromExportedKey(env.UCAN_PRIVATE_KEY)
      const did = await registerDid(env.UCAN, env.NFT_STORAGE_API_TOKEN, kp.did())
      const token = await getServiceUcan(env.UCAN, env.NFT_STORAGE_API_TOKEN)
      const ucan = await build({
        issuer: kp,
        audience: env.NFT_STORAGE_DID,
        lifetimeInSeconds: 1000,
        capabilities: [
          {
            with: `storage://${did}`,
            can: 'upload/IMPORT',
          },
        ],
        proofs: [token],
      })
      return new JsonResponse({ token: ucan, did })
    } catch (err) {
      return errorHandler(err as HTTPError)
    }
  }
}

export default worker
