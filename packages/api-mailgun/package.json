{"name": "mailgun", "version": "0.0.0", "private": true, "type": "module", "scripts": {"clean": "rm -rf node_modules package-lock.json .wrangler", "vars": "echo '' > .dev.vars && echo MAILGUN_KEY=$MAILGUN_KEY >> .dev.vars >> .dev.vars", "dev": "wrangler dev", "prepublish": "echo $MAILGUN_KEY | wrangler secret put MAILGUN_KEY", "publish": "wrangler deploy"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241112.0", "typescript": "^5.6.3", "wrangler": "^3.86.1"}, "dependencies": {"lib-worker": "*"}}