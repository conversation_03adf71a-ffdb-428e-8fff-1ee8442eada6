import { getActorById, propertiesSymbol, updateActor } from 'backend/activitypub/actors'
import { Prompts } from 'backend/ai/prompts'
import { loadLocalMastodonAccount } from 'backend/mastodon/account'
import { getActorTags, getTags, insertHashtags, parseHashtags, updateActorTags } from 'backend/mastodon/hashtag'
import { Featured } from 'types/featured'
import { urlToHandle } from 'backend/utils/handle'
import { sha256 } from 'backend/utils/hash'
import { wsPublish } from 'backend/ws/stream'

interface Env {
  DB: D1Database
  AI: any
  DOMAIN: string
  WS: Service
}

const worker: ExportedHandler<Env, { actorId: string }> = {
  async queue({ messages }, env) {
    try {
      for (const { body: { actorId } } of messages) {
        console.log('Actor', actorId)
        const actor = await getActorById(env.DB, new URL(actorId))
        const text = actor?.summary
        if (!text) {
          console.log('Actor.summary is empty')
          continue
        }
        const properties = actor[propertiesSymbol]
        const hash = await sha256(text)
        if (properties.checksum === hash) {
          console.log('Actor.summary is same')
          continue
        }

        let hashtags = parseHashtags(text)
        console.log('Hashtags from content', hashtags)

        try {
          const { response } = await env.AI.run('@cf/meta/llama-2-7b-chat-int8', { messages: Prompts.tags.input(text) })
          const tags = Prompts.tags.parse(response)
          console.log('Hashtags from ai', tags)
          hashtags = Array.from(new Set(hashtags.concat(tags)).values())
        } catch (err: any) {
          console.error(err.stack, err.cause)
        }

        if (hashtags.length == 0) {
          console.log('Hashtags is empty')
          continue
        }

        const savedTags = await getTags(env.DB, env.DOMAIN, { profileId: actor.id, history: true })
        console.log('Current hashtags', savedTags)

        const tags = await insertHashtags(env.DB, env.DOMAIN, hashtags)
        console.log('Saved hashtags', tags)
        const tagIds = tags.map(({ id }) => id)

        properties.checksum = hash
        properties.tags = tagIds
        await updateActor(env.DB, properties, actor.id)

        await updateActorTags(env.DB, { publishedDate: 'now' }, { tagIds })
        const actorTags = await getActorTags(env.DB, { tagIds })
        for (const { id, actorId, tagId, publishedDate, readDate } of actorTags) {
          const tag = tags.find(({ id }) => id === tagId)
          if (tag) {
            const item: Featured = {
              id,
              publishedDate,
              readDate,
              tag
            }
            await wsPublish(env.WS, `timeline:featured:${urlToHandle(actorId)}`, { type: 'update', item })
          }
        }

        const profile = await loadLocalMastodonAccount(env.DB, actor, true)
        for (const tag of savedTags) {
          const type = tagIds.includes(tag.id) ? 'update' : 'delete'
          await wsPublish(env.WS, `timeline:tag:${tag.id}:profiles`, { type, account: profile })
        }
        const savedTagIds = savedTags.map(({ id }) => id)
        for (const tag of tags.filter(({ id }) => !savedTagIds.includes(id))) {
          await wsPublish(env.WS, `timeline:tag:${tag.id}:profiles`, { type: 'update', account: profile })
        }
      }
    } catch (err: any) {
      console.error(err.stack, err.cause)
    }
  }
}

export default worker
