import { getActorById } from 'backend/activitypub/actors'
import { getObjectByMastodonId, propertiesSymbol as objectPropertiesSymbol, updateObject } from 'backend/activitypub/objects'
import type { Note } from 'backend/activitypub/objects/note'
import { getEmbeddings } from 'backend/ai/embeddings'
import { Prompts } from 'backend/ai/prompts'
import { createNoteTags, getActorTags, getTags, insertHashtags, parseHashtags, updateActorTags } from 'backend/mastodon/hashtag'
import { toMastodonStatusFromObject } from 'backend/mastodon/status'
import { urlToHandle } from 'backend/utils/handle'
import { sha256 } from 'backend/utils/hash'
import { wsPublish } from 'backend/ws/stream'
import type { Featured } from 'types/featured'
import type { NoteMessageBody } from 'types/queue'
import { upsertAllActors, upsertObjects } from './objects'

interface Env {
  DB: D1Database
  AI: any
  VECTORIZE_INDEX: VectorizeIndex
  DO_CACHE: DurableObjectNamespace
  wsService: Service
  DOMAIN: string
}

const worker: ExportedHandler<Env, NoteMessageBody> = {
  async queue(batch, env) {
    const embeddings = getEmbeddings(env.VECTORIZE_INDEX, env.AI)
    for (const { body: { type, mastodonId, actorId } } of batch.messages) {
      if (!['create', 'update', 'delete'].includes(type)) {
        console.warn('unsupported message type: ' + type)
        continue
      }
      if (!mastodonId) {
        console.warn(`mastodonId is missing`)
        continue
      }
      if (!actorId) {
        console.warn('object actorId not found')
        continue
      }
      const actor = await getActorById(env.DB, new URL(actorId))
      if (actor === null) {
        console.warn('object actor not found')
        continue
      }

      if (['create', 'update'].includes(type)) {
        const obj = await getObjectByMastodonId(env.DB, mastodonId)
        if (obj === null) {
          console.warn('object not found')
          continue
        }
        const objectProperties = obj[objectPropertiesSymbol]
        const content = obj?.content
        if (content !== undefined) {
          const hash = await sha256(content)
          if (objectProperties.checksum !== hash) {
            objectProperties.checksum = hash
            await updateObject(env.DB, obj, objectProperties)

            let hashtags = parseHashtags(objectProperties.text)
            console.log('Hashtags from content', hashtags)
            try {
              const { response } = await env.AI.run('@cf/meta/llama-2-7b-chat-int8', { messages: Prompts.tags.input(objectProperties.text) })
              const tags = Prompts.tags.parse(response)
              console.log('Hashtags from ai', tags)
              hashtags = Array.from(new Set(hashtags.concat(tags)).values())
            } catch (err: any) {
              console.error(err.stack, err.cause)
            }

            if (hashtags.length > 0) {
              const savedTags = await getTags(env.DB, env.DOMAIN, { objectId: obj.id, history: true })
              console.log('Current hashtags', savedTags)

              const tags = await insertHashtags(env.DB, env.DOMAIN, hashtags)
              await createNoteTags(env.DB, obj.id.toString(), tags)
              console.log('Saved hashtags', tags)

              const tagIds = tags.map(({ id }) => id)
              await updateActorTags(env.DB, { publishedDate: 'now' }, { tagIds })
              const actorTags = await getActorTags(env.DB, { tagIds })
              for (const { id, actorId, tagId, publishedDate, readDate } of actorTags) {
                const tag = tags.find(({ id }) => id === tagId)
                if (tag) {
                  const item: Featured = {
                    id,
                    publishedDate,
                    readDate,
                    tag
                  }
                  await wsPublish(env.wsService, `timeline:featured:${urlToHandle(actorId)}`, { type: 'update', item })
                }
              }

              const status = await toMastodonStatusFromObject(env.DB, obj as Note, env.DOMAIN)
              for (const tag of savedTags) {
                const type = tagIds.includes(tag.id) ? 'update' : 'delete'
                await wsPublish(env.wsService, `timeline:tag:${tag.id}`, { type, status })
              }
            }

            try {
              await embeddings.upsert(mastodonId, objectProperties.text, { type: 'note' })
            } catch (err: any) {
              console.error(err.stack, err.cause)
            }
          }
        }
      }
      if (type === 'delete') {
        try {
          await embeddings.remove(mastodonId)
        } catch (err: any) {
          console.error(err.stack, err.cause)
        }
      }

      await upsertObjects(env.DB, embeddings, actorId)
      await upsertAllActors(env.DB, embeddings)
      await upsertAllActors(env.DB, embeddings)
    }
  }
}

export default worker
