import { PUBLIC_GROUP } from "backend/activitypub/activities"
import { getActors } from "backend/activitypub/actors"
import { getObjectsBy, mastodonIdSymbol, propertiesSymbol } from "backend/activitypub/objects"
import { NOTE } from "backend/activitypub/objects/note"
import { EmbeddingsId, type EmbeddingsType } from 'backend/ai/embeddings'

export async function upsertObjects(db: D1Database, embeddings: EmbeddingsType, actorId: string) {
  try {
    const objects = await getObjectsBy(db, { actorId, type: NOTE, target: PUBLIC_GROUP, notReplyTo: true })
    const content = objects.map(obj => obj?.[propertiesSymbol]?.text).map(text => text?.replace(/\s+/, ' ').trim())?.join(' ')?.trim()
    const id = EmbeddingsId.fromActorUrl(actorId)
    console.log('content', id, content?.length)
    await embeddings.update(id, content, { type: 'profile' })
  } catch (err: any) {
    console.error(err.stack, err.cause)
  }
}

export async function upsertAllObjects(db: D1Database, embeddings: EmbeddingsType) {
  try {
    const objects = await getObjectsBy(db, { type: NOTE, target: PUBLIC_GROUP, notReplyTo: true })
    for (const obj of objects) {
      const id = obj?.[mastodonIdSymbol]
      const content = obj?.[propertiesSymbol]?.text?.replace(/\s+/, ' ')?.trim()
      console.log('content', id, content?.length)
      await embeddings.update(id, content, { type: 'note' })
    }
  } catch (err: any) {
    console.error(err.stack, err.cause)
  }
}

export async function upsertAllActors(db: D1Database, embeddings: EmbeddingsType) {
  const actors = await getActors(db, {})
  for (const actor of actors) {
    await upsertObjects(db, embeddings, actor.id.toString())
  }
}
