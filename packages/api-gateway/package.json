{"name": "gateway", "version": "0.0.0", "private": true, "type": "module", "scripts": {"clean": "rm -rf node_modules package-lock.json .wrangler", "vars": "VAPID_JWK=$(node ./scripts/generate-vapid-keys.mjs) && echo VAPID_JWK=$VAPID_JWK > .dev.vars && echo BACKBLAZE_KEY=$BACKBLAZE_KEY >> .dev.vars && echo OPENAI_API_KEY=$OPENAI_API_KEY >> .dev.vars", "db:schema": "wrangler d1 export DB --remote --no-data --output schema.sql", "db": "wrangler d1 execute DB --local --command", "db:migrations": "wrangler d1 migrations apply DB", "db:create-migrations": "wrangler d1 migrations create DB", "predev": "npm run db:migrations -- --local", "dev": "wrangler dev", "dev:remote": "wrangler dev --remote", "logs": "wrangler tail", "prepublish:vars": "echo $BACKBLAZE_KEY | wrangler secret put BACKBLAZE_KEY && echo $OPENAI_API_KEY | wrangler secret put OPENAI_API_KEY", "prepublish:db": "wrangler d1 migrations apply DB --remote", "prepublish": "npm-run-all --parallel prepublish:*", "publish": "wrangler deploy"}, "dependencies": {"@hono/sentry": "^1.2.0", "@hono/zod-validator": "^0.2.2", "@ssttevee/multipart-parser": "^0.1.9", "ai": "^3.4.33", "eventsource-parser": "^2.0.1", "hono": "^4.6.9", "lib-worker": "*", "openai-edge": "^1.2.2", "radash": "^12.1.0", "remarkable": "^2.0.1", "tiny-decode": "^0.1.3"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241112.0", "@types/lodash.merge": "^4.6.9", "@types/remarkable": "^2.0.8", "npm-run-all": "^4.1.5", "typescript": "^5.6.3", "wrangler": "^3.86.1"}}