import { sentry } from '@hono/sentry'
import { <PERSON><PERSON>, <PERSON><PERSON>, MiddlewareHandler } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { StatusCode } from 'hono/utils/http-status'
import { Env } from '~/index'

export type RouteHandler = Handler<Env>
export type RouteMiddlewareHandler = MiddlewareHandler<Env>

const app = new Hono<Env>()

app.use('*', sentry())

app.onError((err, c) => {
  let status: StatusCode = 500
  if (err instanceof HTTPException) {
    if (err.res) {
      return err.res
    }
    status = err.status
  }
  console.error({
    message: err.message,
    cause: (err.cause as Error)?.message,
    stack: err.stack,
  })
  return c.text(err.message, status)
})

export default app
