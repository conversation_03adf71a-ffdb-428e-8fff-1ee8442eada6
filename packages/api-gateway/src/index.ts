import type { Person } from '~/backend/activitypub/actors'
import type { MessageBody, NoteMessageBody } from '~/types/queue'
import app from './routes'

interface Bind extends Record<string, unknown> {
  SESSION_COOKIE: string
  ACCESS_AUTH_DOMAIN: string
  ACCESS_AUD: string
  USER_KEY: string
  OTP: KVNamespace
  DB: D1Database
  ucanService: Service
  fidoService: Service
  mailService: Service
  sessionService: Service
  chatService: Service
  wsService: Service

  DATABASE: D1Database
  QUEUE: Queue<MessageBody>
  QUEUE_NOTE: Queue<NoteMessageBody>
  QUEUE_PROFILE: Queue<{ actorId: string }>
  DO_CACHE: DurableObjectNamespace
  BUCKET: R2Bucket

  // Configuration for the instance
  INSTANCE_TITLE: string
  ADMIN_EMAIL: string
  INSTANCE_DESCR: string
  VAPID_JWK: string
  DOMAIN: string

  BACKBLAZE_KEY: string
  BACKBLAZE_KEY_ID: string
  BACKBLAZE_BUCKET_ID: string

  OPENAI_API_KEY: string

  MOCK_TOKEN: string

  AI: Ai
  VECTORIZE_INDEX: VectorizeIndex
}

interface Variables extends Record<string, unknown> {
  token?: string
  // ActivityPub Person object of the logged in user
  person?: Person | null
  // Client or app identifier
  clientId?: string
}

export interface Env {
  Bindings: Bind
  Variables: Partial<Variables>
}

export default app
