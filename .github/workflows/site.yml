name: Site
on:
  push:
    branches:
      - main
    paths:
      - 'packages/site/**'
      - '.github/workflows/site.yml'
      - 'package.json'

jobs:
  deploy:
    name: Deploy site
    runs-on: ubuntu-latest

    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          cache: "npm"

      - name: Install dependencies
        run: npm ci --quiet --no-progress

      - name: Build
        run: |
          npm run build -w site

      - name: Deploy
        run: npm run publish -w site
        env:
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          AUTH_SECRET: ${{ secrets.AUTH_SECRET }}
          BACKBLAZE_KEY: ${{ secrets.BACKBLAZE_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
