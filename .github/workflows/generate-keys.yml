name: Generate keys

on:
  workflow_dispatch:

jobs:
  generate:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v4

      - name: Generate VAPID
        run: |
          node ./scripts/generate-vapid-keys.mjs > vapid.txt
          cat vapid.txt | npx wrangler secret put VAPID_JWK
          rm vapid.txt
        working-directory: ./packages/site
        env:
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
