name: API cloudflare
on:
  push:
    branches:
      - main
    paths:
      - 'packages/lib-*/**'
      - 'packages/queue-*/**'
      - 'packages/api-*/**'
      - '.github/workflows/api-cloudflare.yml'
      - 'package.json'

jobs:
  deploy:
    name: Deploy api cloudflare
    runs-on: ubuntu-latest
    timeout-minutes: 60

    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          cache: "npm"

      - name: Install dependencies
        run: npm ci --quiet --no-progress

      - name: Build
        run: npm run build:lib

      - name: Publish
        run: npm run publish:api
        env:
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          MAILGUN_KEY: ${{ secrets.MAILGUN_KEY }}
          AUTH_SECRET: ${{ secrets.AUTH_SECRET }}
          BACKBLAZE_KEY: ${{ secrets.BACKBLAZE_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
